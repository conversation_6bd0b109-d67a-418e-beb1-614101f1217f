import pygame
import random
import time
import threading
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum

from game.models.player_refactored import Player
from game.models.enemy import Enemy
from game.core.battle_calculator import BattleCalculator
from game.core.luck_system import LuckSystem
from game.models.skill_loader import SkillLoader

# 导入新的日志系统
from .log_manager import Log<PERSON>ana<PERSON>, get_log_manager
from game.managers.user_message_manager import UserMessageType, UserMessagePriority
from .error_message_converter import convert_error_to_user_message
from .debug_config_manager import get_debug_config_manager


class BattleResult(Enum):
    """战斗结果枚举"""
    WIN = "win"            # 胜利
    LOSE = "lose"          # 失败
    ESCAPE = "escape"      # 逃跑
    DRAW = "draw"          # 平局
    ONGOING = "ongoing"    # 进行中


class BattleType(Enum):
    """战斗类型枚举"""
    PVE = "pve"            # 玩家对战环境(怪物)
    PVP = "pvp"            # 玩家对战玩家
    BOSS = "boss"          # Boss战
    ARENA = "arena"        # 竞技场
    DUNGEON = "dungeon"    # 副本


class BattleStateManager:
    """战斗状态管理器 - 统一管理战斗状态和结束条件"""
    
    def __init__(self):
        self.battle_ended = False
        self.winner = None
        self.end_reason = None
    
    def reset(self):
        """重置战斗状态"""
        self.battle_ended = False
        self.winner = None
        self.end_reason = None
    
    def check_battle_end_conditions(self, player, monster):
        """统一检查战斗结束条件"""
        if not player:
            self.battle_ended = True
            self.winner = "monster"
            self.end_reason = "player_missing"
            return True
        
        if hasattr(player, 'current_hp') and player.current_hp <= 0:
            self.battle_ended = True
            self.winner = "monster" 
            self.end_reason = "player_death"
            return True
        elif hasattr(player, 'hp') and player.hp <= 0:
            self.battle_ended = True
            self.winner = "monster"
            self.end_reason = "player_death"
            return True
        
        if not monster:
            self.battle_ended = True
            self.winner = "player"
            self.end_reason = "monster_missing"
            return True
        
        if hasattr(monster, 'current_hp') and monster.current_hp <= 0:
            self.battle_ended = True
            self.winner = "player"
            self.winner = "player"
            self.end_reason = "monster_death"
            return True
        elif hasattr(monster, 'hp') and monster.hp <= 0:
            self.battle_ended = True
            self.winner = "player"
            self.winner = "player"
            self.end_reason = "monster_death"
            return True
        
        return False


class BattleManager:
    """
    战斗管理器类
    负责管理战斗的整个流程
    """
    def __init__(self, data_manager=None, skill_loader=None, debug_console_output=False):
        """
        初始化战斗管理器

        参数:
            data_manager: 数据管理器实例（可选）
            skill_loader: 技能加载器实例（可选）
            debug_console_output: 是否启用控制台调试输出（默认关闭以节省内存）
        """
        # 调试输出控制 - 使用全局配置
        self.debug_config = get_debug_config_manager()
        self.debug_console_output = debug_console_output or self.debug_config.is_console_output_enabled("battle_manager")

        # 初始化技能加载器
        self.skill_loader = skill_loader if skill_loader is not None else SkillLoader()

        # 技能系统相关属性
        self.skill_cooldowns = {}  # 技能冷却时间记录
        self.last_skill_use_time = {}  # 上次使用技能的时间
        self.auto_skill_enabled = True  # 自动释放技能开关
        self.skill_rotation = []  # 技能轮换列表
        self.data_manager = data_manager

        # 集成新的日志系统
        self.log_manager = get_log_manager()

        # 日志节流机制 - 防止重复消息
        self._last_log_messages = {}  # 记录最后一次日志消息的时间
        self._log_throttle_interval = 5.0  # 同一消息的最小间隔时间（秒）

        # 公告面板引用（用于显示掉落信息）
        self.announcement_panel = None
        
        # 战斗状态
        self.is_in_battle = False
        self.current_enemy = None
        self.battle_log = []  # 保持兼容性
        
        # 添加状态管理器
        self.state_manager = BattleStateManager()
        
        # 防重入锁，防止同一帧内多次处理战斗
        self._processing_battle = False
        
        # 战斗回调
        self.callbacks = {
            "on_monster_death": [],
            "on_player_death": [],
            "on_battle_start": [],
            "on_battle_end": [],
            "on_damage_dealt": [],
            "on_damage_taken": []
        }
        
        self.battle_type = BattleType.PVE
        self.attacker = None      # 攻击方
        self.defender = None      # 防守方
        self.turn_count = 0       # 回合计数
        self.max_turns = 30       # 最大回合数
        self.result = BattleResult.ONGOING
        self.rewards = {          # 战斗奖励
            "xp": 0,
            "gold": 0,
            "items": []
        }
        
        # 实时战斗系统相关
        self.last_update_time = time.time()
        self.attacker_action_progress = 0.0  # 攻击方行动进度 (0.0-1.0)
        self.defender_action_progress = 0.0  # 防守方行动进度 (0.0-1.0)
        self.battle_start_time = 0.0         # 战斗开始时间
        self.max_battle_duration = 60.0     # 最大战斗时长(秒) - 1分钟限制
        self.battle_time_limit_enabled = True  # 战斗时间限制开关
        
        # 行动进度相关
        self.action_progress_speed = 1.0     # 进度条填充速度
        self.last_action_time = 0.0          # 上次行动时间
        
        # 自动战斗系统相关
        self.auto_battle_enabled = False    # 是否启用自动战斗
        self.auto_battle_stopping = False   # 是否正在停止自动战斗
        
        # 自动寻怪系统相关
        self.auto_hunt_enabled = False      # 自动寻怪总开关
        self.current_target_enemy = None    # 当前目标怪物
        self.hunt_path = []                 # 寻怪路径
        self.hunt_state = "idle"            # 寻怪状态: idle/hunting/moving/fighting
        self.last_hunt_time = 0.0           # 上次寻怪时间
        self.hunt_interval = 2.0            # 寻怪间隔(秒)
        self.movement_speed = 10.0          # 移动速度(像素/秒)，降低速度让移动更明显
        self.detection_range = 50.0         # 怪物检测范围，减小检测范围避免过早发现远距离怪物
        
        # 玩家复活系统相关
        self.player_is_dead = False         # 玩家是否死亡
        self.revival_time = 10.0            # 复活时间(秒)
        self.death_time = 0.0              # 死亡时间戳
        self.revival_start_time = 0.0       # 复活开始时间戳
        
        # 外部引用
        self.mini_map_panel = None  # 小地图面板引用
        self.player_instance = None  # 玩家实例引用
        self.map_manager = None     # 地图管理器引用
        self.battle_panel = None    # 战斗面板引用，用于显示伤害文本
        
        # 位置跟踪和寻路可视化
        self._current_player_pos = (25, 25)  # 当前玩家位置
        self._movement_trail = []            # 移动轨迹历史
        self._max_trail_length = 50         # 最大轨迹长度
        
    def setup_battle(self, attacker, defender):
        """
        设置战斗双方 - 增强版，确保战斗正确开始
        
        Args:
            attacker: 攻击方实体
            defender: 防守方实体
        """
        try:
            self._log_event(f"正在设置战斗: {getattr(attacker, 'name', '玩家')} vs {getattr(defender, 'name', '怪物')}")
            
            self.attacker = attacker
            self.defender = defender
            
            # 重置战斗状态管理器
            self.state_manager.reset()
            
            # 重置战斗状态
            if hasattr(attacker, 'reset_combat'):
                attacker.reset_combat()
            if hasattr(defender, 'reset_combat'):
                defender.reset_combat()
            
            # 计算双方战斗力
            try:
                self.attacker.combat_value = BattleCalculator.calculate_combat_value(attacker)
                self.defender.combat_value = BattleCalculator.calculate_combat_value(defender)
            except Exception as e:
                self._log_event(f"计算战斗力时出错: {e}")
                # 设置默认战斗力
                self.attacker.combat_value = getattr(attacker, 'level', 1) * 100
                self.defender.combat_value = getattr(defender, 'level', 1) * 100
            
            # 重置战斗数据
            self.turn_count = 0
            self.battle_log = []
            self.result = BattleResult.ONGOING
            self.rewards = {"xp": 0, "gold": 0, "items": []}
            
            # 设置战斗状态
            self.is_in_battle = True
            self.current_enemy = defender
            
            # 记录战斗开始时间
            self.battle_start_time = time.time()
            
            # 重置行动进度条
            self.reset_action_progress()
            
            self._log_event("战斗设置完成，即将开始战斗！")
            
            # 触发战斗开始回调
            self._trigger_callback("on_battle_start", attacker, defender)
            
        except Exception as e:
            self._log_event(f"设置战斗时出错: {e}")
            import traceback
            self._log_event(f"错误详情: {traceback.format_exc()}")
    
    def _log_event(self, message, show_to_user=True, console_output=None):
        """
        记录战斗事件 - 增强版，确保消息被正确输出，并支持日志节流

        参数:
            message: 日志消息
            show_to_user: 是否显示给用户
            console_output: 是否输出到控制台（None=使用全局设置，True=强制输出，False=不输出）
        """
        try:
            # 日志节流检查：防止相同消息频繁输出
            current_time = time.time()
            message_hash = hash(message)

            if message_hash in self._last_log_messages:
                if current_time - self._last_log_messages[message_hash] < self._log_throttle_interval:
                    # 消息太频繁，跳过这次输出
                    return

            # 更新最后消息时间
            self._last_log_messages[message_hash] = current_time

            # 添加时间戳
            timestamp = time.strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {message}"

            # 1. 添加到内部战斗日志（保持兼容性）
            if hasattr(self, 'battle_log'):
                self.battle_log.append({
                    'message': message,
                    'timestamp': timestamp,
                    'time': time.time()
                })

            # 2. 使用新的日志系统
            if hasattr(self, 'log_manager') and self.log_manager and show_to_user:
                from .log_manager import MessageType
                # 使用LogManager的add_user_message方法和MessageType
                self.log_manager.add_user_message(
                    message,
                    MessageType.BATTLE
                )

            # 3. 控制台输出（可配置）
            should_print = False
            if console_output is not None:
                # 明确指定是否输出
                should_print = console_output
            else:
                # 使用全局调试设置
                should_print = getattr(self, 'debug_console_output', False)

            if should_print:
                print(f"🎮 {formatted_message}")

            # 4. 确保消息被传递到UI系统
            if show_to_user and hasattr(self, '_ui_message_callback') and self._ui_message_callback:
                self._ui_message_callback(message)

        except Exception as e:
            # 即使日志系统出错，也要确保重要消息被输出
            if console_output is True or getattr(self, 'debug_console_output', False):
                print(f"🎮 {message}")
                print(f"❌ 日志系统错误: {e}")

        # 定期清理旧的消息记录，防止内存泄漏
        if len(self._last_log_messages) > 100:
            # 清理超过1小时的记录
            cutoff_time = current_time - 3600
            self._last_log_messages = {
                k: v for k, v in self._last_log_messages.items()
                if v > cutoff_time
            }
    
    def _trigger_callback(self, event_type: str, *args):
        """触发回调函数的临时实现"""
        try:
            if event_type in self.callbacks:
                for callback in self.callbacks[event_type]:
                    if callable(callback):
                        callback(*args)
        except Exception as e:
            self._log_event(f"触发回调时出错: {e}")
    
    def toggle_auto_battle(self):
        """切换自动战斗状态 - 改进版，支持真实战斗进度条"""
        try:
            if not self.auto_hunt_enabled:
                # 启动自动寻怪和战斗
                self.auto_hunt_enabled = True
                self.hunt_state = "hunting"
                # 🔧 新增：立即启用自动战斗，确保战斗使用进度条系统
                self.auto_battle_enabled = True
                self._log_event("🚀 开始自动寻怪和战斗！（基于攻击速度的进度条系统）")
                return "自动战斗已启动"
            else:
                # 停止自动寻怪和战斗
                self.auto_hunt_enabled = False
                self.auto_battle_enabled = False
                self.hunt_state = "idle"
                self._log_event("🛑 停止自动寻怪和战斗！")
                return "自动战斗已停止"
        except Exception as e:
            self._log_event(f"切换自动战斗状态时出错: {e}")
            return "自动战斗切换失败"
    
    def update_auto_hunt(self, delta_time):
        """更新自动寻怪系统 - 修复版，改进状态管理"""
        # 首先检查玩家是否死亡
        if self.player_is_dead:
            self._update_revival_state()
            return
            
        if not self.auto_hunt_enabled:
            return
            
        current_time = time.time()
        
        try:
            # 更新小地图数据 - 确保数据同步
            self.update_minimap_data()
            
            if self.hunt_state == "hunting":
                # 检查是否该寻找新目标了
                if current_time - self.last_hunt_time >= self.hunt_interval:
                    self._find_and_target_enemy()
                    self.last_hunt_time = current_time
                
            elif self.hunt_state == "moving":
                # 移动到目标位置
                if self.current_target_enemy:
                    self._move_towards_target(delta_time)
                else:
                    # 没有目标，回到寻怪状态
                    self.hunt_state = "hunting"
                    
            elif self.hunt_state == "fighting":
                # 战斗进行中，更新战斗状态
                if self.is_in_battle:
                    self.update_battle(current_time)
                else:
                    # 战斗结束，回到寻怪状态
                    self._log_event("🔄 战斗结束，继续寻找下一个目标...")
                    self.hunt_state = "hunting"
                    self.current_target_enemy = None
                    
        except Exception as e:
            self._log_event(f"❌ 更新自动寻怪时出错: {e}")
            # 出错时重置到寻怪状态
            self.hunt_state = "hunting"
    
    def _find_and_target_enemy(self):
        """寻找并锁定敌人 - 修复版，改进寻路流程"""
        try:
            self._log_event("🔍 正在搜索附近的怪物...")
            
            # 尝试从地图获取最近的敌人
            enemy = self._get_nearest_enemy_from_minimap()
            
            if enemy:
                self.current_target_enemy = enemy
                distance = enemy.get('distance', 0)
                enemy_name = enemy.get('name', '未知怪物')
                
                self._log_event(f"🎯 发现目标怪物: {enemy_name} (距离: {distance:.1f})")
                
                # 更严格的距离检查 - 只有距离大于5像素才需要移动
                if distance <= 5.0:
                    # 距离很近，直接开始战斗
                    self._log_event(f"💥 目标很近，直接开始战斗！")
                    self._start_auto_battle_with_enemy()
                else:
                    # 需要移动到目标位置
                    target_pos = enemy.get('position', (0, 0))
                    self._log_event(f"📍 目标距离 {distance:.1f} 像素，开始向目标位置移动: {target_pos}")
                    self.hunt_state = "moving"
            else:
                # 没找到敌人，检查是否需要重新生成怪物
                if not hasattr(self, '_last_monster_load_time') or time.time() - self._last_monster_load_time > 5.0:
                    self._log_event("❌ 附近没有发现怪物，尝试重新生成怪物...")
                    
                    # 检查当前地图是否有怪物生成能力
                    if hasattr(self, 'map_manager') and self.map_manager and self.map_manager.current_map:
                        current_map = self.map_manager.current_map
                        
                        # 如果地图上没有怪物，重新生成
                        if not hasattr(current_map, 'active_enemies') or len(current_map.active_enemies) == 0:
                            if hasattr(current_map, 'spawn_monsters') and hasattr(self.map_manager, 'data_manager'):
                                self._log_event("🔄 地图上没有怪物，重新生成怪物...")
                                current_map.spawn_monsters(self.map_manager.data_manager)
                                self._log_event(f"✅ 重新生成了 {len(current_map.active_enemies)} 个怪物")
                            elif hasattr(current_map, 'generate_enemies'):
                                self._log_event("🔄 使用简单模式重新生成怪物...")
                                current_map.generate_enemies(10)
                                self._log_event(f"✅ 重新生成了 {len(current_map.active_enemies)} 个怪物")
                    
                    self._last_monster_load_time = time.time()
                else:
                    # 如果最近刚加载过，就暂停一下再继续搜索
                    self.hunt_state = "idle"
                    self.auto_hunt_enabled = False
                    self._log_event("⏸️ 当前地图无法生成更多怪物，自动寻怪已停止。请手动切换地图。")
                
        except Exception as e:
            self._log_event(f"❌ 寻找敌人时出错: {e}")
            # 出错时不要立即开始战斗，回到寻怪状态
            self.hunt_state = "hunting"
    
    def _get_nearest_enemy_from_minimap(self):
        """从小地图获取最近的敌人 - 修复版，真正使用小地图数据"""
        try:
            # 更新小地图数据
            self.update_minimap_data()
            
            # 获取玩家位置
            player_x, player_y = 25, 25  # 默认玩家位置
            if hasattr(self, 'player_instance') and self.player_instance:
                player_x = getattr(self.player_instance, 'x', 25)
                player_y = getattr(self.player_instance, 'y', 25)
            
            # 使用地图管理器的find_nearest_enemy方法 - 这是关键！
            if hasattr(self, 'map_manager') and self.map_manager and self.map_manager.current_map:
                current_map = self.map_manager.current_map
                
                # 🔧 新增：寻找安全的怪物，避开危险怪物
                safe_enemy, safe_distance = self._find_safe_enemy_from_map(current_map, (player_x, player_y))

                if safe_enemy and safe_distance is not None:
                    return {
                        'name': getattr(safe_enemy, 'name', '未知怪物'),
                        'type': getattr(safe_enemy, 'enemy_type', '普通'),
                        'position': getattr(safe_enemy, 'position', (0, 0)),
                        'distance': safe_distance,
                        'enemy_object': safe_enemy  # 保存真实的敌人对象
                    }
                else:
                    # 如果没有找到安全的怪物，使用原来的方法但记录警告
                    nearest_enemy, distance = current_map.find_nearest_enemy(
                        player_pos=(player_x, player_y),
                        enemy_type=None,  # 寻找任意类型的怪物
                        max_distance=self.detection_range
                    )

                    if nearest_enemy and distance is not None:
                        # 检查这个怪物是否危险
                        if self._is_dangerous_monster(nearest_enemy):
                            self._log_event(f"⚠️ 发现危险怪物 {getattr(nearest_enemy, 'name', '未知')}，建议谨慎应对")

                        return {
                            'name': getattr(nearest_enemy, 'name', '未知怪物'),
                            'type': getattr(nearest_enemy, 'enemy_type', '普通'),
                            'position': getattr(nearest_enemy, 'position', (0, 0)),
                            'distance': distance,
                            'enemy_object': nearest_enemy  # 保存真实的敌人对象
                        }
                    else:
                        self._log_event(f"🔍 在侦测范围({self.detection_range})内未发现怪物")
                        return None
            
            return None
            
        except Exception as e:
            self._log_event(f"❌ 从地图获取敌人数据时出错: {e}")
            return None
        
    def _check_enemy_in_range(self):
        """检查敌人是否在攻击范围内 - 修复版，实现真正的距离检查"""
        if not self.current_target_enemy:
            return False

        try:
            # 获取玩家位置
            player_x, player_y = 25, 25  # 默认玩家位置
            if hasattr(self, 'player_instance') and self.player_instance:
                player_x = getattr(self.player_instance, 'x', 25)
                player_y = getattr(self.player_instance, 'y', 25)

            # 🔧 修复：确保 current_target_enemy 不为 None 再调用 get 方法
            if self.current_target_enemy is None:
                return False

            # 获取敌人位置
            enemy_pos = self.current_target_enemy.get('position', (0, 0))
            enemy_x, enemy_y = enemy_pos
            
            # 计算距离
            distance = ((enemy_x - player_x) ** 2 + (enemy_y - player_y) ** 2) ** 0.5
            
            # 检查是否在攻击范围内（设置为2.0像素，确保需要真正接近才能战斗）
            attack_range = 2.0
            in_range = distance <= attack_range
            
            if in_range:
                self._log_event(f"✅ 已接近目标，准备战斗！距离: {distance:.1f} 像素")
            else:
                # 只在距离变化较大时才输出日志，避免刷屏
                if not hasattr(self, '_last_logged_distance') or abs(distance - self._last_logged_distance) > 5.0:
                    self._log_event(f"🚶 距离目标 {distance:.1f} 像素，需要继续移动（攻击范围: {attack_range}）")
                    self._last_logged_distance = distance
            
            return in_range
            
        except Exception as e:
            self._log_event(f"检查攻击范围时出错: {e}")
            return False  # 修复：出错时返回False，不要立即开始战斗
    
    def _start_auto_battle_with_enemy(self):
        """开始与敌人的自动战斗 - 改进版，使用真实进度条战斗系统"""
        if not self.current_target_enemy:
            self._log_event("❌ 无当前目标敌人，无法开始战斗")
            return

        self.hunt_state = "fighting"

        # 记录开始战斗
        enemy_name = self.current_target_enemy.get('name', '未知怪物')
        self._log_event(f"⚔️ 开始与 {enemy_name} 战斗！（进度条战斗系统）")
        
        # 尝试使用真实的敌人对象
        try:
            # 🔧 修复：在每次访问前都检查 current_target_enemy 是否为 None
            if self.current_target_enemy is None:
                self._log_event("❌ 当前目标敌人为空，无法开始战斗")
                self.hunt_state = "hunting"
                return

            enemy = self.current_target_enemy.get('enemy_object')

            # 如果没有真实的敌人对象，尝试创建一个
            if enemy is None:
                from game.models.enemy import enemy_factory

                # 再次检查，防止在上面的操作过程中被清空
                if self.current_target_enemy is None:
                    self._log_event("❌ 目标敌人在创建过程中被清空")
                    self.hunt_state = "hunting"
                    return

                enemy_type = self.current_target_enemy.get('type', '普通')
                
                # 根据敌人类型设置等级调整
                level_adjust = 0
                if enemy_type == "精英":
                    level_adjust = 1
                elif enemy_type == "首领":
                    level_adjust = 2
                
                # 检查当前是否在世界BOSS地图
                is_world_boss_map = False
                if hasattr(self, 'map_manager') and self.map_manager:
                    current_map = self.map_manager.get_current_map()
                    if current_map and hasattr(current_map, 'is_world_boss'):
                        is_world_boss_map = current_map.is_world_boss
                
                # 使用全局EnemyFactory实例创建敌人
                # 再次检查并获取敌人名称，防止在创建过程中被清空
                if self.current_target_enemy is None:
                    self._log_event("❌ 目标敌人在创建敌人前被清空")
                    self.hunt_state = "hunting"
                    return

                current_enemy_name = self.current_target_enemy.get('name', '未知怪物')
                enemy = enemy_factory.create_enemy(current_enemy_name, level_adjust, enemy_type, None, is_world_boss_map)
            
            # 检查敌人是否创建成功
            if enemy is None:
                self._log_event(f"❌ 创建敌人 {current_enemy_name} 失败，可能是模板不存在")
                self.hunt_state = "hunting"
                return
            
            # 🔧 确保怪物有攻击速度属性
            if not hasattr(enemy, 'attack_speed'):
                # 根据怪物类型设置不同的攻击速度
                if enemy_type == "精英":
                    enemy.attack_speed = 1.2  # 精英怪攻击较快
                elif enemy_type == "首领":
                    enemy.attack_speed = 0.8  # 首领攻击较慢但威力大
                else:
                    enemy.attack_speed = 1.0  # 普通怪标准攻击速度
                    
                self._log_event(f"🎯 设置 {current_enemy_name} 攻击速度: {enemy.attack_speed}")
            
            # 如果有玩家实例，设置战斗
            if self.player_instance:
                # 🔧 确保玩家有攻击速度属性 - 使用现有的属性系统
                player_attack_speed = 1.0
                
                # 优先使用stats系统的攻击速度
                if hasattr(self.player_instance, 'stats') and hasattr(self.player_instance.stats, 'attack_speed'):
                    player_attack_speed = self.player_instance.stats.attack_speed
                # 其次使用直接的攻速属性
                elif hasattr(self.player_instance, '攻速'):
                    player_attack_speed = getattr(self.player_instance, '攻速')
                # 最后使用attack_speed属性
                elif hasattr(self.player_instance, 'attack_speed'):
                    player_attack_speed = self.player_instance.attack_speed
                else:
                    # 根据玩家职业设置基础攻击速度
                    character_class = getattr(self.player_instance, 'character_class', '战士')
                    if character_class == '战士':
                        player_attack_speed = 1.0  # 战士标准速度
                    elif character_class == '法师':
                        player_attack_speed = 0.9  # 法师施法较慢
                    elif character_class == '道士':
                        player_attack_speed = 1.1  # 道士攻击较快
                    else:
                        player_attack_speed = 1.0
                    
                    # 设置到玩家对象上
                    self.player_instance.attack_speed = player_attack_speed
                
                # 🔧 可以根据装备调整攻击速度
                if hasattr(self.player_instance, 'equipment_manager'):
                    equipment_stats = self.player_instance.equipment_manager.get_equipment_stats()
                    # 🔧 修复：检查 equipment_stats 是否为 None
                    if equipment_stats is not None:
                        equipment_attack_speed_bonus = equipment_stats.get('攻速', 0)
                        if equipment_attack_speed_bonus > 0:
                            player_attack_speed += equipment_attack_speed_bonus
                            self._log_event(f"🛡️ 装备攻速加成: +{equipment_attack_speed_bonus:.2f}")
                    else:
                        self._log_event("⚠️ 装备属性为空，跳过攻速加成计算")
                
                # 确保玩家对象有attack_speed属性
                self.player_instance.attack_speed = player_attack_speed
                    
                self._log_event(f"🎯 玩家攻击速度: {player_attack_speed:.2f}")
                
                # 开始真实战斗，使用进度条系统
                success = self.start_battle(self.player_instance, enemy)
                if success:
                    # 强制启用自动战斗以使用进度条系统
                    self.auto_battle_enabled = True
                    self._log_event(f"✅ 真实战斗设置完成！进度条系统已激活")
                    self._log_event(f"📊 战斗数据: 玩家速度{player_attack_speed:.2f} vs 怪物速度{enemy.attack_speed:.2f}")
                else:
                    self._log_event("❌ 战斗设置失败")
                    self.hunt_state = "hunting"
            else:
                self._log_event("❌ 无法获取玩家实例，战斗设置失败")
                self.hunt_state = "hunting"
            
        except Exception as e:
            import traceback
            self._log_event(f"❌ 创建战斗时发生错误: {str(e)}")
            self._log_event(f"❌ 错误详情: {traceback.format_exc()}")
            self.hunt_state = "hunting"
    
    def _move_towards_target(self, delta_time):
        """向目标移动 - 增强版，包含轨迹记录和可视化"""
        if not self.current_target_enemy:
            self._log_event("❌ 无当前目标敌人，无法移动")
            return
            
        try:
            # 获取玩家当前位置 - 优先使用战斗管理器跟踪的位置
            player_x, player_y = self._current_player_pos
            
            # 获取目标位置
            target_pos = self.current_target_enemy.get('position', (0, 0))
            target_x, target_y = target_pos
            
            # 计算移动方向
            dx = target_x - player_x
            dy = target_y - player_y
            distance = (dx * dx + dy * dy) ** 0.5
            
            # 检查是否已经接近目标
            if distance <= 2.0:
                self._log_event(f"📍 已接近目标位置（距离: {distance:.1f}），开始战斗")
                self._start_auto_battle_with_enemy()
                return
            
            if distance > 0:
                # 标准化方向向量
                dx_norm = dx / distance
                dy_norm = dy / distance
                
                # 计算这一帧的移动距离
                move_distance = self.movement_speed * delta_time
                
                # 防止超调，如果剩余距离小于移动距离，直接移动到目标附近
                if move_distance >= distance - 1.5:
                    # 移动到距离目标1.5像素的位置
                    new_x = target_x - dx_norm * 1.5
                    new_y = target_y - dy_norm * 1.5
                else:
                    new_x = player_x + dx_norm * move_distance
                    new_y = player_y + dy_norm * move_distance
                
                # 更新位置跟踪
                self._current_player_pos = (new_x, new_y)
                
                # 记录移动轨迹
                self._movement_trail.append((new_x, new_y))
                if len(self._movement_trail) > self._max_trail_length:
                    self._movement_trail.pop(0)
                
                # 同步更新玩家实例位置（如果支持）
                if hasattr(self, 'player_instance') and self.player_instance:
                    if hasattr(self.player_instance, 'position'):
                        self.player_instance.position = (new_x, new_y)
                    elif hasattr(self.player_instance, 'x') and hasattr(self.player_instance, 'y'):
                        self.player_instance.x = new_x
                        self.player_instance.y = new_y
                
                # 重新计算距离以检查是否到达
                new_distance = ((target_x - new_x) ** 2 + (target_y - new_y) ** 2) ** 0.5
                
                # 每1秒输出一次移动进度，避免刷屏
                current_time = time.time()
                if current_time - getattr(self, '_last_move_log_time', 0) >= 1.0:
                    self._log_event(f"🚶 正在移动到目标位置... 剩余距离: {new_distance:.1f} 像素")
                    self._last_move_log_time = current_time
                
                # 检查是否到达攻击范围（使用新距离）
                if new_distance <= 2.0:
                    self._log_event(f"📍 已到达目标位置（距离: {new_distance:.1f}），开始战斗")
                    self._start_auto_battle_with_enemy()
            
        except Exception as e:
            self._log_event(f"移动时出错: {e}")
            # 出错时回到寻怪状态，不要立即开始战斗
            self.hunt_state = "hunting"
            self.current_target_enemy = None
    
    def _load_minimap_monster_data(self):
        """加载小地图怪物数据 - 优化版，避免重复日志和无意义调用"""
        try:
            current_time = time.time()
            
            # 检查是否需要重新加载数据
            if hasattr(self, '_last_data_load_time') and current_time - self._last_data_load_time < 10.0:
                # 10秒内已经加载过，跳过
                return
            
            # 更新加载时间
            self._last_data_load_time = current_time
            
            # 检查地图管理器是否可用
            if not hasattr(self, 'map_manager') or not self.map_manager:
                self._log_event("⚠️ 地图管理器未初始化，无法加载怪物数据")
                return
            
            if not self.map_manager.current_map:
                self._log_event("⚠️ 当前没有活动地图，无法加载怪物数据")
                return
            
            current_map = self.map_manager.current_map
            
            # 检查地图是否有怪物配置
            if not hasattr(current_map, 'spawn_monsters') or not hasattr(self.map_manager, 'data_manager'):
                self._log_event("⚠️ 地图配置不完整，无法生成怪物")
                return
            
            # 检查是否已经有足够的怪物
            if hasattr(current_map, 'active_enemies') and len(current_map.active_enemies) > 0:
                # 已经有怪物，只在需要时才重新生成
                if not self.auto_hunt_enabled:
                    return  # 没有自动寻怪，不需要重新生成
            
            # 开始重新生成怪物
            self._log_event("🔄 正在重新加载怪物数据...")
            
            current_map.spawn_monsters(self.map_manager.data_manager)
            
            if hasattr(current_map, 'active_enemies') and current_map.active_enemies:
                self._log_event(f"✅ 成功重新生成 {len(current_map.active_enemies)} 个怪物")
            else:
                self._log_event("⚠️ 当前地图配置中没有怪物，请选择其他地图")
                # 如果没有怪物且正在自动寻怪，停止自动寻怪
                if self.auto_hunt_enabled:
                    self.auto_hunt_enabled = False
                    self.hunt_state = "idle"
                    self._log_event("🛑 已停止自动寻怪，请前往有怪物的地图")
                
        except Exception as e:
            self._log_event(f"❌ 重新加载怪物数据时出错: {e}")
            # 出错时停止自动寻怪
            if self.auto_hunt_enabled:
                self.auto_hunt_enabled = False
                self.hunt_state = "idle"
    
    def start_battle(self, player, enemy):
        """开始战斗 - 改进版，确保攻击速度系统正确初始化"""
        try:
            # 🔧 确保战斗正确开始
            player_name = getattr(player, 'name', '玩家')
            enemy_name = getattr(enemy, 'name', '怪物')
            player_speed = getattr(player, 'attack_speed', 1.0)
            enemy_speed = getattr(enemy, 'attack_speed', 1.0)
            
            self._log_event(f"🚀 开始真实战斗: {player_name}(速度{player_speed}) vs {enemy_name}(速度{enemy_speed})")
            
            # 首先设置战斗双方
            self.setup_battle(player, enemy)
            
            # 强制确保战斗状态正确设置
            self.is_in_battle = True
            self.current_enemy = enemy
            
            # 重置并初始化进度条
            self.reset_action_progress()
            
            # 🔧 不给初始进度，让攻击速度自然决定进度
            # 这样玩家可以看到真实的速度差异
            self.attacker_action_progress = 0.0
            self.defender_action_progress = 0.0
            
            # 🔧 记录战斗开始时的攻击速度信息
            speed_advantage = player_speed / enemy_speed if enemy_speed > 0 else 1.0
            if speed_advantage > 1.1:
                self._log_event(f"⚡ 玩家攻击速度优势: {speed_advantage:.2f}倍，将更频繁攻击！")
            elif speed_advantage < 0.9:
                self._log_event(f"⚠️ 敌人攻击速度优势: {1/speed_advantage:.2f}倍，小心应对！")
            else:
                self._log_event(f"⚖️ 双方攻击速度相近，进入均势战斗！")
            
            self._log_event(f"✅ 真实战斗系统启动！攻击时机由进度条决定")
            return True
            
        except Exception as e:
            self._log_event(f"开始战斗时出错: {e}")
            return False
    
    def update_battle(self, current_time):
        """更新战斗状态 - 只保留真实战斗，去除演示动画"""
        # 首先检查玩家是否死亡
        if self.player_is_dead:
            self._update_revival_state()
            return
        
        try:
            # 计算delta时间（pygame.time.get_ticks()返回毫秒）
            if not hasattr(self, '_last_update_time'):
                self._last_update_time = current_time
            delta_time_ms = current_time - self._last_update_time
            delta_time = delta_time_ms / 1000.0  # 转换为秒用于计算
            self._last_update_time = current_time
            
            # 确保delta_time有意义
            if delta_time < 0 or delta_time > 1.0:  # 防止异常的时间跳跃
                delta_time = 1.0 / 60.0  # 默认60FPS
            
            # 🔧 只在真实战斗时更新进度条
            if self.is_in_battle and self.attacker and self.defender:
                # 正常战斗模式
                self.update_action_progress(delta_time)
                
                # 检查战斗时间限制
                if self.battle_time_limit_enabled:
                    battle_elapsed_time = (current_time / 1000.0) - self.battle_start_time
                    if battle_elapsed_time >= self.max_battle_duration:
                        self._log_event("⏰ 战斗时间到达1分钟限制，怪物未死亡，战斗失败！")
                        self.state_manager.battle_ended = True
                        self.state_manager.winner = "monster"
                        self.state_manager.end_reason = "time_limit"
                        self._end_battle()
                        return
                
                # 🔧 调试：记录进度条状态（仅在调试模式下输出）
                if self.debug_console_output and (not hasattr(self, '_debug_last_log_time') or (current_time - self._debug_last_log_time) > 5000):  # 每5秒记录一次
                    self._log_event(f"🔧 战斗进度 - 攻击方: {self.attacker_action_progress:.1f}, 防守方: {self.defender_action_progress:.1f}", console_output=True)
                    self._debug_last_log_time = current_time
                
                # 检查战斗是否结束
                if self.state_manager.check_battle_end_conditions(self.attacker, self.defender):
                    self._end_battle()
                    return
                
                # 🔧 基于进度条的战斗逻辑：当进度条满时才出手
                action_triggered = False
                
                # 检查攻击方（玩家）是否可以行动
                if self.attacker_action_progress >= 1.0:
                    self._log_event(f"🔧 攻击方进度条满({self.attacker_action_progress:.3f})，执行攻击", console_output=False)
                    self._execute_attack_action(self.attacker, self.defender)
                    self.attacker_action_progress = 0.0  # 重置攻击方进度
                    action_triggered = True

                # 检查防守方（怪物）是否可以行动
                if self.defender_action_progress >= 1.0:
                    self._log_event(f"🔧 防守方进度条满({self.defender_action_progress:.3f})，执行攻击", console_output=False)
                    self._execute_attack_action(self.defender, self.attacker)
                    self.defender_action_progress = 0.0  # 重置防守方进度
                    action_triggered = True
                    
                # 如果有行动发生，记录时间
                if action_triggered:
                    self.last_action_time = current_time / 1000.0
            # 🔧 去除演示模式，不在战斗时不显示进度条动画
                
        except Exception as e:
            self._log_event(f"更新战斗时出错: {e}")
            import traceback
            self._log_event(f"错误详情: {traceback.format_exc()}")
    
    def _execute_attack_action(self, attacker, defender):
        """执行单个攻击行动"""
        try:
            attacker_name = getattr(attacker, 'name', '未知')
            defender_name = getattr(defender, 'name', '未知')
            
            # 计算伤害 - 使用真实属性计算
            base_damage, is_critical, final_damage = BattleCalculator.calculate_damage(
                attacker, defender, skill_power=1.0, damage_type="physical"
            )
            
            # 如果计算失败，使用备用计算
            if final_damage == 0 and base_damage == 0:
                if hasattr(attacker, 'calculate_attack'):
                    is_critical = BattleCalculator.check_critical(attacker, defender)
                    damage = attacker.calculate_attack(is_critical)
                else:
                    # 最后的备用方案
                    damage = random.randint(5, 15) if attacker == self.attacker else random.randint(3, 8)
                    is_critical = random.random() < (0.15 if attacker == self.attacker else 0.05)
                    if is_critical:
                        damage = int(damage * 1.5)
                
                # 应用伤害
                if hasattr(defender, 'take_damage'):
                    final_damage = defender.take_damage(damage)
                else:
                    final_damage = damage
                    if hasattr(defender, 'current_hp'):
                        defender.current_hp = max(0, defender.current_hp - damage)
            
            self._log_event(f"{attacker_name} 对 {defender_name} 造成了 {final_damage} 点伤害{'(暴击!)' if is_critical else ''}")
            
            # 显示伤害文本
            if self.battle_panel:
                if attacker == self.attacker:  # 玩家攻击怪物
                    self.battle_panel.show_enemy_damage(final_damage, 0, is_critical)
                else:  # 怪物攻击玩家
                    self.battle_panel.show_player_damage(final_damage, is_critical)
            
            # 检查目标是否死亡
            defender_hp = getattr(defender, 'current_hp', getattr(defender, 'hp', 0))
            if defender_hp <= 0:
                self._log_event(f"{defender_name} 被击败了！")
                if defender == self.defender:  # 怪物死亡
                    self._handle_battle_victory()
                else:  # 玩家死亡
                    self.state_manager.battle_ended = True
                    self.state_manager.winner = "monster"
                    self.state_manager.end_reason = "player_death"
                    self._handle_battle_defeat()
                
        except Exception as e:
            self._log_event(f"执行攻击行动时出错: {e}")
    
    def _execute_battle_turn(self):
        """执行一个战斗回合 - 保留兼容性，但现在使用新的行动系统"""
        # 这个方法现在主要用于向后兼容，实际战斗逻辑已移到 _execute_attack_action
        pass
    
    def _end_battle(self):
        """结束战斗"""
        try:
            self.is_in_battle = False
            self.auto_battle_enabled = False
            
            if self.state_manager.winner == "player":
                self._handle_battle_victory()
            else:
                self._handle_battle_defeat()
                
            # 重置战斗状态
            self.attacker = None
            self.defender = None
            self.current_enemy = None
            
            # 重置当前目标怪物，让系统寻找新的目标
            self.current_target_enemy = None
            
            # 如果还在自动寻怪，继续寻找下一个目标
            if self.auto_hunt_enabled:
                self.hunt_state = "hunting"
                self._log_event("🔄 准备寻找下一个目标...")
                # 设置一个短暂的延迟（2秒），让玩家看到战斗结束过程
                self.last_hunt_time = time.time() - self.hunt_interval + 2.0
                
        except Exception as e:
            self._log_event(f"结束战斗时出错: {e}")
    
    def _handle_battle_victory(self):
        """处理战斗胜利"""
        try:
            if self.defender:
                exp_gain = getattr(self.defender, 'exp', 10)
                gold_gain = getattr(self.defender, 'gold', 5)
                monster_name = getattr(self.defender, 'name', '未知怪物')
                monster_level = getattr(self.defender, 'level', 1)
                
                # 判断是否为Boss（通过名称或属性判断）
                is_boss = False
                if hasattr(self.defender, 'type') and self.defender.type == 'BOSS':
                    is_boss = True
                elif 'boss' in monster_name.lower() or '教主' in monster_name or '王' in monster_name:
                    is_boss = True
                elif hasattr(self.defender, 'is_boss'):
                    is_boss = self.defender.is_boss
                
                self._log_event(f"战斗胜利！击败 {'Boss' if is_boss else '怪物'} [{monster_name}]")
                self._log_event(f"获得 {exp_gain} 经验值和 {gold_gain} 金币")
                
                # ✨ 新增：处理怪物掉落
                player_level = getattr(self.player_instance, 'level', 1) if self.player_instance else 1
                dropped_items = self._handle_monster_drops(monster_name, player_level, is_boss)
                
                # 显示掉落信息
                if dropped_items:
                    self._log_event(f"💎 {monster_name} 掉落了 {len(dropped_items)} 件物品:")
                    for item in dropped_items:
                        rarity_icon = self._get_rarity_icon(item.rarity)
                        if item.quantity > 1:
                            self._log_event(f"  {rarity_icon} {item.name} x{item.quantity}")
                        else:
                            self._log_event(f"  {rarity_icon} {item.name}")
                    
                    # 尝试将物品添加到玩家背包
                    self._add_items_to_inventory(dropped_items)
                else:
                    self._log_event(f"💸 {monster_name} 没有掉落任何物品")
                
                # 关键修复：从地图上移除被击败的怪物
                if hasattr(self, 'map_manager') and self.map_manager and self.map_manager.current_map:
                    current_map = self.map_manager.current_map
                    if hasattr(current_map, 'remove_enemy'):
                        current_map.remove_enemy(self.defender)
                        self._log_event(f"🗑️ 已从地图移除被击败的怪物: {monster_name}")
                
                # 记录击杀统计 - 新增功能
                if hasattr(self, 'player_instance') and self.player_instance:
                    # 获取当前地图名称
                    current_map_name = None
                    if hasattr(self, 'map_manager') and self.map_manager:
                        if hasattr(self.map_manager, 'current_map_name'):
                            current_map_name = self.map_manager.current_map_name
                        elif hasattr(self.map_manager, 'current_map') and self.map_manager.current_map:
                            current_map_name = self.map_manager.current_map.name
                    
                    # 如果玩家有击杀统计方法，则记录击杀
                    if hasattr(self.player_instance, 'add_monster_kill'):
                        self.player_instance.add_monster_kill(
                            monster_name=monster_name,
                            map_name=current_map_name,
                            is_boss=is_boss
                        )
                        
                        # 显示击杀统计信息
                        stats = self.player_instance.get_kill_statistics()
                        if is_boss:
                            self._log_event(f"📊 总击杀统计: 怪物{stats['total_monsters']}, Boss{stats['total_bosses']}")
                        else:
                            if stats['total_monsters'] % 10 == 0:  # 每10个怪物显示一次统计
                                self._log_event(f"📊 总击杀统计: 怪物{stats['total_monsters']}, Boss{stats['total_bosses']}")
                
                # 在这里可以添加经验值和金币到玩家
                old_level = getattr(self.player_instance, 'level', 1) if self.player_instance else 1

                if hasattr(self.player_instance, 'add_exp'):
                    self.player_instance.add_exp(exp_gain)
                if hasattr(self.player_instance, 'add_currency'):
                    self.player_instance.add_currency(gold_gain)

                # 🔧 新增：检测升级并通知公告面板
                new_level = getattr(self.player_instance, 'level', 1) if self.player_instance else 1
                if new_level > old_level and self.announcement_panel:
                    self.announcement_panel.important_manager.add_level_up(old_level, new_level)
                    self._log_event(f"🎉 恭喜升级！{old_level} → {new_level}")

                    # 添加升级奖励信息（如果有的话）
                    self.announcement_panel.add_important_info(
                        f"🌟 升级奖励：属性提升，技能点+1", "升级", "重要", (255, 215, 0)
                    )
                
        except Exception as e:
            self._log_event(f"处理战斗胜利时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def _handle_monster_drops(self, monster_name: str, player_level: int, is_boss: bool = False):
        """处理怪物掉落物品（仅使用配置文件掉落）"""
        try:
            # 重新加载物品配置以确保最新数据
            from game.systems.item_generator import reload_item_generator
            reload_item_generator()
            
            from game.systems.item_generator import item_generator
            
            # 获取掉落物品
            dropped_items = item_generator.get_monster_drops(monster_name, player_level)
            
            if dropped_items:
                # 显示掉落信息
                drop_messages = []
                for item in dropped_items:
                    drop_messages.append(f"获得 {item.name} x{item.quantity}")

                    # 🔧 新增：通知公告面板掉落信息
                    if self.announcement_panel:
                        # 获取当前地图名称
                        current_location = "未知地图"
                        if hasattr(self, 'map_manager') and self.map_manager and self.map_manager.current_map:
                            current_location = getattr(self.map_manager.current_map, 'name', '未知地图')

                        # 添加掉落信息到公告面板
                        self.announcement_panel.add_drop_info(
                            item_name=item.name,
                            item_type=getattr(item, 'type', '物品'),
                            rarity=getattr(item, 'rarity', '普通'),
                            source=monster_name,
                            location=current_location
                        )

                # 添加到背包
                self._add_items_to_inventory(dropped_items)

                # 更新用户消息
                if drop_messages:
                    from game.managers.user_message_manager import get_user_message_manager, UserMessageType
                    user_message_manager = get_user_message_manager()

                    # 将掉落消息列表转换为单个字符串
                    drop_message_text = "\n".join(drop_messages)
                    user_message_manager.add_message(
                        message=drop_message_text,
                        msg_type=UserMessageType.INFO,
                        color=(100, 255, 100),  # 绿色表示好消息
                        duration=5.0,
                        auto_dismiss=True
                    )
                    
        except Exception as e:
            print(f"🎮 [掉落系统] 处理怪物掉落时出错: {e}")
            import traceback
            print(f"❌ _handle_monster_drops 异常详情: {e}")
            traceback.print_exc()
    
    def _get_rarity_icon(self, rarity: str) -> str:
        """获取稀有度图标"""
        rarity_icons = {
            "common": "⚪",      # 白色 - 普通
            "rare": "🔵",        # 蓝色 - 稀有
            "epic": "🟣",        # 紫色 - 史诗
            "legendary": "🟡"    # 金色 - 传说
        }
        return rarity_icons.get(rarity, "⚪")
    
    def _add_items_to_inventory(self, items):
        """将物品添加到玩家背包（统一背包系统版本）"""
        try:
            if not items:
                return
                
            # 🔧 统一背包系统：只使用一套背包管理器
            success_count = 0
            failed_count = 0
            
            # 添加到玩家的统一背包管理器
            if self.player_instance and hasattr(self.player_instance, 'inventory_manager'):
                inventory_manager = self.player_instance.inventory_manager
                
                for item in items:
                    success = False
                    
                    # 尝试添加物品
                    if hasattr(inventory_manager, 'add_item'):
                        success = inventory_manager.add_item(item)
                    elif hasattr(inventory_manager, 'add_item_by_name'):
                        success = inventory_manager.add_item_by_name(item.name, item.quantity)
                    
                    if success:
                        success_count += 1
                    else:
                        failed_count += 1
                        print(f"❌ 无法添加物品到背包: {item.name}")
                        
                        # 尝试添加到地图作为掉落物品
                        self._add_dropped_item_to_map(item)
                
                if success_count > 0:
                    print(f"✅ 成功添加 {success_count} 个物品到背包")
                if failed_count > 0:
                    print(f"⚠️ {failed_count} 个物品因背包已满掉落到地图")
            else:
                # 如果没有背包管理器，掉落到地图
                for item in items:
                    self._add_dropped_item_to_map(item)
                    
        except Exception as e:
            print(f"🎮 [背包系统] 添加物品到背包时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def _add_dropped_item_to_map(self, item):
        """将物品掉落到地图（背包已满时）"""
        try:
            # 简单的掉落处理：显示提示信息
            print(f"💎 物品掉落到地图: {item.name} x{item.quantity}")
            
            # 可以在这里添加更复杂的地图物品掉落系统
            # 比如：将物品添加到当前地图的掉落物品列表中
            # 这里先用简单的提示代替
            
        except Exception as e:
            print(f"🎮 [地图系统] 添加掉落物品到地图时出错: {e}")
    
    def _handle_battle_defeat(self):
        """处理战斗失败"""
        try:
            # 检查失败原因
            if hasattr(self.state_manager, 'end_reason') and self.state_manager.end_reason == "time_limit":
                self._log_event("⏰ 战斗失败！时间限制到达，怪物未能击败...")
                # 时间限制失败不算玩家死亡，只是战斗失败
                self.player_is_dead = False
            else:
                self._log_event("💀 战斗失败！玩家死亡...")
                # 设置玩家死亡状态
                self.player_is_dead = True
                self.death_time = time.time()
                self.revival_start_time = time.time()

                # 设置玩家血量为0（如果玩家对象存在）
                if hasattr(self, 'player_instance') and self.player_instance:
                    if hasattr(self.player_instance, 'current_hp'):
                        self.player_instance.current_hp = 0
                    elif hasattr(self.player_instance, 'hp'):
                        self.player_instance.hp = 0

                # 🔧 新增：记录导致死亡的怪物，避免复活后立即再次遭遇
                if hasattr(self, 'defender') and self.defender:
                    self._record_death_causing_monster(self.defender)

                    # 🔧 新增：通知公告面板死亡信息
                    if self.announcement_panel:
                        monster_name = getattr(self.defender, 'name', '未知怪物')
                        current_location = "未知地图"
                        if hasattr(self, 'map_manager') and self.map_manager and self.map_manager.current_map:
                            current_location = getattr(self.map_manager.current_map, 'name', '未知地图')

                        self.announcement_panel.important_manager.add_death_info(monster_name, current_location)

                self._log_event(f"⏱️ 复活倒计时: {self.revival_time:.0f} 秒")
            
            # 停止所有自动战斗和寻怪
            self.auto_hunt_enabled = False
            self.auto_battle_enabled = False
            self.hunt_state = "idle"
            
            # 清空当前目标
            self.current_target_enemy = None
            
        except Exception as e:
            self._log_event(f"处理战斗失败时出错: {e}")
    
    def get_battle_log(self):
        """获取战斗日志"""
        return self.battle_log.copy() if hasattr(self, 'battle_log') else []
    
    def get_auto_battle_status(self):
        """获取自动战斗状态文本"""
        try:
            # 如果玩家死亡，显示特殊状态
            if self.player_is_dead:
                return "玩家死亡"
                
            if self.auto_hunt_enabled and self.auto_battle_enabled:
                return "停止战斗"
            elif self.auto_hunt_enabled:
                return "寻怪中..."
            else:
                return "自动战斗"
        except Exception as e:
            self._log_event(f"获取自动战斗状态时出错: {e}")
            return "自动战斗"
    
    def get_enemies(self):
        """获取当前地图的敌人列表"""
        try:
            # 如果有地图管理器，从地图管理器获取敌人
            if hasattr(self, 'map_manager') and self.map_manager and self.map_manager.current_map:
                return self.map_manager.current_map.active_enemies
            
            # 如果正在战斗且有当前敌人，返回当前敌人
            if self.is_in_battle and self.current_enemy:
                return [self.current_enemy]
            
            # 否则返回空列表
            return []
        except Exception as e:
            self._log_event(f"获取敌人列表时出错: {e}")
            return []
    
    def is_auto_battle_active(self):
        """检查自动战斗是否激活"""
        return self.auto_hunt_enabled or self.auto_battle_enabled
    
    def set_mini_map_panel(self, mini_map_panel):
        """设置小地图面板引用"""
        self.mini_map_panel = mini_map_panel
    
    def set_player_instance(self, player_instance):
        """设置玩家实例引用"""
        self.player_instance = player_instance
        
        # 初始化玩家位置
        if player_instance:
            if hasattr(player_instance, 'position') and player_instance.position:
                self._current_player_pos = player_instance.position
            elif hasattr(player_instance, 'x') and hasattr(player_instance, 'y'):
                self._current_player_pos = (player_instance.x, player_instance.y)
            # 添加初始位置到轨迹
            self._movement_trail.append(self._current_player_pos)
    
    def set_map_manager(self, map_manager):
        """设置地图管理器引用"""
        self.map_manager = map_manager
    
    def set_battle_panel(self, battle_panel):
        """设置战斗面板引用"""
        self.battle_panel = battle_panel
    
    def is_auto_finding_active(self):
        """检查是否正在自动寻怪 - 小地图需要的方法"""
        return self.auto_hunt_enabled
    
    def get_player_detection_radius(self):
        """获取玩家侦测半径 - 小地图需要的方法"""
        return self.detection_range
    
    def get_nearby_monsters_relative_to_player(self):
        """获取相对于玩家位置的附近怪物列表 - 小地图需要的方法"""
        try:
            nearby_monsters = []
            
            # 获取玩家位置
            player_x, player_y = 25, 25  # 默认玩家位置
            if hasattr(self, 'player_instance') and self.player_instance:
                player_x = getattr(self.player_instance, 'x', 25)
                player_y = getattr(self.player_instance, 'y', 25)
            
            # 从地图管理器获取怪物数据
            if hasattr(self, 'map_manager') and self.map_manager and self.map_manager.current_map:
                enemies = self.map_manager.current_map.active_enemies
                
                for enemy in enemies:
                    if hasattr(enemy, 'position') and enemy.position:
                        enemy_x, enemy_y = enemy.position
                        
                        # 计算相对位置
                        relative_x = enemy_x - player_x
                        relative_y = enemy_y - player_y
                        
                        # 计算距离
                        distance = (relative_x ** 2 + relative_y ** 2) ** 0.5
                        
                        # 只包含在侦测范围内的怪物
                        if distance <= self.detection_range:
                            nearby_monsters.append((relative_x, relative_y))
            
            return nearby_monsters
            
        except Exception as e:
            self._log_event(f"获取附近怪物时出错: {e}")
            return []
    
    def get_current_auto_hunt_target_relative_position(self):
        """获取当前自动寻怪目标的相对位置 - 小地图需要的方法"""
        try:
            if not self.current_target_enemy:
                return None
            
            # 获取玩家位置
            player_x, player_y = 25, 25  # 默认玩家位置
            if hasattr(self, 'player_instance') and self.player_instance:
                player_x = getattr(self.player_instance, 'x', 25)
                player_y = getattr(self.player_instance, 'y', 25)
            
            # 获取目标位置
            target_pos = self.current_target_enemy.get('position', (0, 0))
            target_x, target_y = target_pos
            
            # 计算相对位置
            relative_x = target_x - player_x
            relative_y = target_y - player_y
            
            return (relative_x, relative_y)
            
        except Exception as e:
            self._log_event(f"获取目标相对位置时出错: {e}")
            return None
    
    def update_minimap_data(self):
        """更新小地图数据 - 确保数据同步"""
        try:
            if hasattr(self, 'mini_map_panel') and self.mini_map_panel:
                # 获取玩家位置
                player_pos = (25, 25)  # 默认位置
                if hasattr(self, 'player_instance') and self.player_instance:
                    player_pos = (
                        getattr(self.player_instance, 'x', 25),
                        getattr(self.player_instance, 'y', 25)
                    )
                
                # 从地图管理器获取小地图数据
                if hasattr(self, 'map_manager') and self.map_manager and self.map_manager.current_map:
                    minimap_data = self.map_manager.current_map.get_minimap_data(player_pos)
                    
                    # 更新小地图面板的数据
                    if hasattr(self.mini_map_panel, 'minimap_data'):
                        self.mini_map_panel.minimap_data = minimap_data
                
        except Exception as e:
            self._log_event(f"更新小地图数据时出错: {e}")
    
    def _update_revival_state(self):
        """更新玩家复活状态"""
        try:
            if not self.player_is_dead:
                return
                
            current_time = time.time()
            elapsed_time = current_time - self.revival_start_time
            remaining_time = self.revival_time - elapsed_time
            
            if remaining_time <= 0:
                # 复活时间到，复活玩家
                self._revive_player()
            else:
                # 每秒更新一次复活倒计时
                if not hasattr(self, '_last_revival_log_time') or current_time - self._last_revival_log_time >= 1.0:
                    self._log_event(f"💀 复活倒计时: {remaining_time:.0f} 秒")
                    self._last_revival_log_time = current_time
                    
        except Exception as e:
            self._log_event(f"更新复活状态时出错: {e}")
    
    def _revive_player(self):
        """复活玩家"""
        try:
            self.player_is_dead = False
            self.death_time = 0.0
            self.revival_start_time = 0.0

            # 恢复玩家血量
            if hasattr(self, 'player_instance') and self.player_instance:
                if hasattr(self.player_instance, 'max_hp'):
                    max_hp = self.player_instance.max_hp
                    if hasattr(self.player_instance, 'current_hp'):
                        self.player_instance.current_hp = max_hp
                    else:
                        self.player_instance.hp = max_hp
                    self._log_event(f"💚 玩家复活！血量已恢复到 {max_hp}")
                else:
                    self._log_event("💚 玩家复活！")

            # 🔧 新增：重新刷新地图怪物，避免玩家陷入死亡循环
            self._refresh_map_monsters_on_revival()

            # 完全重置战斗和自动寻怪状态
            self.hunt_state = "idle"
            self.current_target_enemy = None
            self.is_in_battle = False
            self.auto_hunt_enabled = False
            self.auto_battle_enabled = False

            self._log_event("✨ 复活完成，可以继续冒险了！")

        except Exception as e:
            self._log_event(f"复活玩家时出错: {e}")

    def _refresh_map_monsters_on_revival(self):
        """
        玩家复活时重新刷新地图怪物
        避免玩家陷入死亡循环，给玩家一个新的开始
        """
        try:
            # 检查地图管理器是否可用
            if not hasattr(self, 'map_manager') or not self.map_manager:
                self._log_event("⚠️ 地图管理器未初始化，无法刷新怪物")
                return

            if not self.map_manager.current_map:
                self._log_event("⚠️ 当前没有活动地图，无法刷新怪物")
                return

            current_map = self.map_manager.current_map

            self._log_event("🔄 玩家复活，正在重新刷新地图怪物...")

            # 清除当前地图上的所有怪物
            if hasattr(current_map, 'active_enemies'):
                old_monster_count = len(current_map.active_enemies)
                current_map.active_enemies.clear()
                self._log_event(f"🗑️ 已清除 {old_monster_count} 个旧怪物")

            # 重新生成怪物
            if hasattr(current_map, 'spawn_monsters') and hasattr(self.map_manager, 'data_manager'):
                # 使用数据管理器重新生成怪物
                current_map.spawn_monsters(self.map_manager.data_manager)
                new_monster_count = len(current_map.active_enemies) if hasattr(current_map, 'active_enemies') else 0
                self._log_event(f"✅ 重新生成了 {new_monster_count} 个新怪物")
            elif hasattr(current_map, 'generate_enemies'):
                # 使用简单模式重新生成怪物
                current_map.generate_enemies(10)
                new_monster_count = len(current_map.active_enemies) if hasattr(current_map, 'active_enemies') else 0
                self._log_event(f"✅ 重新生成了 {new_monster_count} 个新怪物（简单模式）")
            else:
                self._log_event("⚠️ 当前地图不支持怪物生成")
                return

            # 重置小地图数据加载时间，强制重新加载
            if hasattr(self, '_last_data_load_time'):
                delattr(self, '_last_data_load_time')

            self._log_event("🌟 地图怪物刷新完成！新的冒险开始了！")

        except Exception as e:
            self._log_event(f"刷新地图怪物时出错: {e}")
            # 即使刷新失败，也尝试基本的怪物重新生成
            try:
                if hasattr(self, 'map_manager') and self.map_manager and self.map_manager.current_map:
                    if hasattr(self.map_manager.current_map, 'generate_enemies'):
                        self.map_manager.current_map.generate_enemies(5)  # 生成少量怪物作为备用
                        self._log_event("🔧 使用备用方案生成了少量怪物")
            except Exception as backup_e:
                self._log_event(f"备用怪物生成也失败: {backup_e}")

    def set_announcement_panel(self, announcement_panel):
        """设置公告面板引用，用于显示掉落信息"""
        self.announcement_panel = announcement_panel

        # 同时添加一些重要信息到公告面板
        if announcement_panel:
            announcement_panel.add_important_info(
                "🎮 战斗系统已连接到公告面板", "系统", "重要", (0, 255, 255)
            )

    def _record_death_causing_monster(self, monster):
        """
        记录导致玩家死亡的怪物信息
        用于在复活后避免立即再次遭遇相同的危险怪物

        参数:
            monster: 导致死亡的怪物对象
        """
        try:
            if not hasattr(self, '_death_causing_monsters'):
                self._death_causing_monsters = []

            # 记录怪物信息
            monster_info = {
                'name': getattr(monster, 'name', '未知怪物'),
                'level': getattr(monster, 'level', 1),
                'position': getattr(monster, 'position', None),
                'death_time': time.time()
            }

            self._death_causing_monsters.append(monster_info)

            # 只保留最近5次死亡记录，避免内存占用过多
            if len(self._death_causing_monsters) > 5:
                self._death_causing_monsters.pop(0)

            self._log_event(f"⚠️ 记录危险怪物: {monster_info['name']} (等级{monster_info['level']})")

        except Exception as e:
            self._log_event(f"记录死亡怪物时出错: {e}")

    def _is_dangerous_monster(self, monster):
        """
        检查怪物是否是最近导致玩家死亡的危险怪物

        参数:
            monster: 要检查的怪物对象

        返回:
            bool: 如果是危险怪物返回True
        """
        try:
            if not hasattr(self, '_death_causing_monsters') or not self._death_causing_monsters:
                return False

            current_time = time.time()
            monster_name = getattr(monster, 'name', '未知怪物')
            monster_level = getattr(monster, 'level', 1)

            # 检查最近30秒内的死亡记录
            for death_record in self._death_causing_monsters:
                if current_time - death_record['death_time'] < 30.0:  # 30秒内的记录
                    if (death_record['name'] == monster_name and
                        abs(death_record['level'] - monster_level) <= 2):  # 等级相近的同名怪物
                        return True

            return False

        except Exception as e:
            self._log_event(f"检查危险怪物时出错: {e}")
            return False

    def _find_safe_enemy_from_map(self, current_map, player_pos):
        """
        从地图中寻找安全的怪物（避开危险怪物）

        参数:
            current_map: 当前地图对象
            player_pos: 玩家位置 (x, y)

        返回:
            tuple: (安全的怪物对象, 距离) 或 (None, None)
        """
        try:
            if not hasattr(current_map, 'active_enemies') or not current_map.active_enemies:
                return None, None

            safe_enemies = []
            player_x, player_y = player_pos

            # 遍历所有怪物，找出安全的怪物
            for enemy in current_map.active_enemies:
                # 跳过危险怪物
                if self._is_dangerous_monster(enemy):
                    continue

                # 计算距离
                enemy_pos = getattr(enemy, 'position', (0, 0))
                if enemy_pos:
                    enemy_x, enemy_y = enemy_pos
                    distance = ((enemy_x - player_x) ** 2 + (enemy_y - player_y) ** 2) ** 0.5

                    # 只考虑在检测范围内的怪物
                    if distance <= self.detection_range:
                        safe_enemies.append((enemy, distance))

            # 如果有安全的怪物，返回最近的一个
            if safe_enemies:
                # 按距离排序，返回最近的
                safe_enemies.sort(key=lambda x: x[1])
                nearest_safe_enemy, distance = safe_enemies[0]

                self._log_event(f"✅ 找到安全目标: {getattr(nearest_safe_enemy, 'name', '未知')} (距离: {distance:.1f})")
                return nearest_safe_enemy, distance
            else:
                self._log_event("⚠️ 检测范围内没有安全的怪物")
                return None, None

        except Exception as e:
            self._log_event(f"寻找安全怪物时出错: {e}")
            return None, None
    
    def get_revival_info(self):
        """获取复活信息，供UI显示使用"""
        if not self.player_is_dead:
            return None
            
        current_time = time.time()
        elapsed_time = current_time - self.revival_start_time
        remaining_time = max(0, self.revival_time - elapsed_time)
        
        return {
            "is_dead": True,
            "remaining_time": remaining_time,
            "total_time": self.revival_time
        }

    def get_action_progress(self):
        """
        获取战斗行动进度
        
        返回:
            tuple: (攻击方进度, 防守方进度)
        """
        return (self.attacker_action_progress, self.defender_action_progress)
    
    def update_action_progress(self, delta_time):
        """
        更新行动进度条 - 真实战斗版，攻击速度决定进度快慢
        
        参数:
            delta_time: 时间增量（秒）
        """
        try:
            # 🔧 获取双方的真实攻击速度
            attacker_speed = 1.0
            defender_speed = 1.0
            
            if self.attacker:
                attacker_speed = getattr(self.attacker, 'attack_speed', 1.0)
            if self.defender:
                defender_speed = getattr(self.defender, 'attack_speed', 1.0)
            
            # 🔧 真实战斗模式：基础进度速度调整
            # 攻击速度1.0 = 每1.5秒一次攻击 = 每秒0.67进度
            base_progress_per_second = 0.67
            
            # 🔧 重要：攻击速度直接影响进度条填充速度
            # 攻击速度越高，进度条填充越快，攻击频率越高
            attacker_progress_increment = base_progress_per_second * attacker_speed * delta_time
            defender_progress_increment = base_progress_per_second * defender_speed * delta_time
            
            # 🔧 添加调试信息（仅在真实战斗时）
            if self.is_in_battle and hasattr(self, '_last_speed_debug_time'):
                current_time = time.time()
                if current_time - self._last_speed_debug_time > 3.0:  # 每3秒输出一次
                    self._log_event(f"⚡ 攻击速度影响 - 玩家: {attacker_speed:.2f}→{attacker_progress_increment:.3f}/秒, 怪物: {defender_speed:.2f}→{defender_progress_increment:.3f}/秒")
                    self._last_speed_debug_time = current_time
            elif self.is_in_battle:
                self._last_speed_debug_time = time.time()
            
            # 只有当进度小于1.0时才继续增加
            if self.attacker_action_progress < 1.0:
                self.attacker_action_progress += attacker_progress_increment
                self.attacker_action_progress = min(1.0, self.attacker_action_progress)
            
            if self.defender_action_progress < 1.0:
                self.defender_action_progress += defender_progress_increment
                self.defender_action_progress = min(1.0, self.defender_action_progress)
            
        except Exception as e:
            self._log_event(f"更新行动进度时出错: {e}")
    
    def reset_action_progress(self):
        """
        重置行动进度
        """
        self.attacker_action_progress = 0.0
        self.defender_action_progress = 0.0
        self.last_action_time = time.time()
        self._log_event(f"🔧 进度条已重置 - 攻击方: {self.attacker_action_progress}, 防守方: {self.defender_action_progress}")
        
        # 重置调试标志，让下次战斗重新记录
        if hasattr(self, '_debug_battle_start_logged'):
            delattr(self, '_debug_battle_start_logged')
    
    def get_battle_time_remaining(self):
        """获取战斗剩余时间（秒）"""
        if not self.is_in_battle or not self.battle_time_limit_enabled:
            return None
        
        current_time = time.time()
        elapsed_time = current_time - self.battle_start_time
        remaining_time = max(0, self.max_battle_duration - elapsed_time)
        return remaining_time

# 创建全局战斗管理器实例
battle_manager = BattleManager()