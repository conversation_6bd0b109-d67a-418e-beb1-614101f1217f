"""
游戏主入口文件
用于初始化游戏并启动主界面
"""
import os
import sys
import pygame
import tkinter as tk
import json
import random
import time
from threading import Thread

# 确保可以导入游戏模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入UI模块
from game.ui.ui_manager import UIManager
from game.ui.skill_panel import SkillPanel
from game.ui.skill_hotbar import AutoSkillHotbar

# 定义游戏窗口大小
WINDOW_WIDTH = 1280
WINDOW_HEIGHT = 720

# 定义颜色
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GRAY = (50, 50, 50)

# 导入重构后的Player类
from game.models.player_refactored import Player

# 创建简单的MapManager类
class MapManager:
    def __init__(self, data_manager=None):
        # 数据管理器引用
        self.data_manager = data_manager
        
        # BOSS挑战次数
        self.boss_challenge = {"current": 15, "max_daily": 15}
        
        # Boss刷新时间管理
        self.boss_respawn_times = {}  # 存储每个地图Boss的刷新时间
        
        # 初始化一些测试Boss刷新时间数据
        import time
        current_time = time.time()
        # 模拟一些Boss已经被击杀的情况
        self.boss_respawn_times["沃玛寺庙"] = current_time - 1200  # 20分钟前被击杀
        self.boss_respawn_times["祖玛寺庙"] = current_time - 5400  # 1.5小时前被击杀
        self.boss_respawn_times["封魔谷"] = current_time - 10800  # 3小时前被击杀（应该已刷新）
        self.boss_respawn_times["赤月峡谷"] = current_time - 3600   # 1小时前被击杀
        
        # 自动挑战状态
        self.auto_challenge_enabled = False  # 是否开启自动挑战Boss
        self.boss_challenge_storage = 10  # Boss挑战存储次数，最多10次
        
        # 加载地图配置文件
        self.available_maps = []
        self.load_maps_from_config()
        
        # 地图配置缓存
        self.maps_config = {}
        
        # 当前地图和怪物管理
        self.current_map = None
        self.current_map_name = "比奇省"
        self.active_enemies = []  # 当前地图的活跃怪物列表
        
        # 初始化当前地图的怪物
        self._initialize_current_map_enemies()
    
    def _initialize_current_map_enemies(self):
        """
        初始化当前地图的怪物 - 改进版，使用分散分布避免扎堆
        """
        # 生成一些测试怪物数据
        import random
        from game.models.enemy import Enemy
        
        self.active_enemies = []
        # 用于记录已占用的位置，确保怪物分散
        occupied_positions = set()
        min_distance = 4  # 怪物之间最小距离
        
        def is_position_valid(pos_x, pos_y, min_dist, occupied_pos):
            """检查位置是否有效（不与其他怪物太近）"""
            for occupied_x, occupied_y in occupied_pos:
                distance = ((pos_x - occupied_x) ** 2 + (pos_y - occupied_y) ** 2) ** 0.5
                if distance < min_dist:
                    return False
            return True
        
        def find_scattered_position(min_dist, occupied_pos):
            """寻找分散的位置"""
            max_attempts = 100
            
            for attempt in range(max_attempts):
                # 使用网格化分布策略避免扎堆
                if attempt < 70:
                    # 网格化分布
                    grid_size = 6
                    grid_x = random.randint(0, (50 - 1) // grid_size)
                    grid_y = random.randint(0, (50 - 1) // grid_size)
                    # 在网格内随机偏移
                    pos_x = grid_x * grid_size + random.randint(2, grid_size - 1)
                    pos_y = grid_y * grid_size + random.randint(2, grid_size - 1)
                else:
                    # 备用随机位置
                    pos_x = random.randint(5, 45)
                    pos_y = random.randint(5, 45)
                
                # 确保位置在有效范围内
                pos_x = max(5, min(pos_x, 45))
                pos_y = max(5, min(pos_y, 45))
                
                # 检查位置是否有效
                if is_position_valid(pos_x, pos_y, min_dist, occupied_pos):
                    return pos_x, pos_y
            
            # 降级处理：返回随机位置
            print("警告: 无法找到合适的分散位置，使用随机位置")
            return random.randint(5, 45), random.randint(5, 45)
        
        # 如果有数据管理器，尝试从配置加载怪物
        if self.data_manager:
            try:
                # 生成一些基础怪物 - 使用monsters.json中的真实怪物名称
                monster_names = ["鸡", "鹿", "蛤蟆", "稻草人", "半兽人", "半兽战士", "半兽勇士", "骷髅"]
                total_monsters = 10  # 总怪物数量
                
                for i in range(total_monsters):
                    monster_name = random.choice(monster_names)
                    monster_data = self.data_manager.get_monster_data(monster_name)
                    
                    if monster_data:
                        # 复制怪物数据并设置分散位置
                        enemy_data = monster_data.copy()
                        
                        # 使用分散分布算法
                        pos_x, pos_y = find_scattered_position(min_distance, occupied_positions)
                        enemy_data["position"] = (pos_x, pos_y)
                        
                        # 记录占用位置
                        occupied_positions.add((pos_x, pos_y))
                        
                        enemy = Enemy(enemy_data)
                        self.active_enemies.append(enemy)
                    else:
                        # 如果没有找到怪物数据，创建默认怪物
                        pos_x, pos_y = find_scattered_position(min_distance, occupied_positions)
                        
                        default_enemy_data = {
                            "name": monster_name,
                            "level": random.randint(1, 10),
                            "hp": random.randint(50, 200),
                            "max_hp": random.randint(50, 200),
                            "attack": random.randint(10, 30),
                            "defense": random.randint(5, 15),
                            "exp": random.randint(10, 50),
                            "gold": random.randint(5, 25),
                            "type": random.choice(["普通", "精英"]),
                            "position": (pos_x, pos_y)
                        }
                        
                        # 记录占用位置
                        occupied_positions.add((pos_x, pos_y))
                        
                        enemy = Enemy(default_enemy_data)
                        self.active_enemies.append(enemy)
                        
                print(f"怪物初始化完成: {len(self.active_enemies)} 个怪物分散分布在 {len(occupied_positions)} 个位置")
                        
            except Exception as e:
                print(f"初始化怪物时出错: {e}")
    
    def get_minimap_data(self):
        """
        获取小地图数据
        
        返回:
            包含玩家位置和怪物信息的字典
        """
        minimap_data = {
            "player_pos": (25, 25),  # 默认玩家位置
            "enemies": []
        }
        
        # 添加怪物信息
        for enemy in self.active_enemies:
            if hasattr(enemy, 'position') and enemy.position:
                enemy_info = (
                    enemy.position[0],
                    enemy.position[1],
                    getattr(enemy, 'type', '普通'),
                    getattr(enemy, 'name', '未知怪物')
                )
                minimap_data["enemies"].append(enemy_info)
        
        return minimap_data
    
    def find_nearest_enemy(self, player_pos, enemy_type=None):
        """
        寻找最近的怪物
        
        参数:
            player_pos: 玩家位置 (x, y)
            enemy_type: 怪物类型过滤（可选）
            
        返回:
            最近的怪物对象或None
        """
        nearest_enemy = None
        min_distance = float('inf')
        
        for enemy in self.active_enemies:
            if hasattr(enemy, 'position') and enemy.position:
                # 如果指定了怪物类型，进行过滤
                if enemy_type and getattr(enemy, 'type', '') != enemy_type:
                    continue
                
                # 计算距离
                dx = enemy.position[0] - player_pos[0]
                dy = enemy.position[1] - player_pos[1]
                distance = (dx * dx + dy * dy) ** 0.5
                
                if distance < min_distance:
                    min_distance = distance
                    nearest_enemy = enemy
        
        return nearest_enemy
    
    def remove_enemy(self, enemy):
        """
        从地图中移除怪物
        
        参数:
            enemy: 要移除的怪物对象
        """
        if enemy in self.active_enemies:
            self.active_enemies.remove(enemy)
    
    def update_game_state(self):
        """
        更新游戏状态
        """
        # 检查自动挑战Boss
        self.check_auto_boss_challenge()
    
    def check_auto_boss_challenge(self):
        """
        检查是否需要自动挑战Boss
        """
        # 如果自动挑战未开启，直接返回
        if not self.auto_challenge_enabled:
            return
            
        # 获取当前地图数据
        current_map_data = None
        for map_data in self.available_maps:
            if map_data.get("is_world_boss", False):
                current_map_data = map_data
                break
                
        if not current_map_data:
            return
            
        # 检查是否可以挑战Boss
        if self.can_challenge_boss(current_map_data):
            boss_info = self.get_current_map_boss_info(current_map_data)
            if boss_info and boss_info["is_available"]:
                # 自动开始Boss挑战
                if self.start_boss_challenge(current_map_data, is_manual=False):
                    print(f"自动挑战Boss: {current_map_data.get('name')}")
    
    def load_maps_from_config(self):
        """
        从配置文件加载地图数据
        """
        try:
            # 配置文件路径 - 使用资源管理器获取地图配置文件路径
            from game.core.resource_manager import get_game_data_path
            config_path = get_game_data_path("maps_config.json")
            
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                maps_config = json.load(f)
            
            # 将配置转换为地图列表格式
            map_id = 1
            for map_name, map_data in maps_config['maps'].items():
                map_entry = {
                    "id": f"map_{map_id:03d}",
                    "name": map_name,
                    "description": map_data.get("description", ""),
                    "difficulty": map_data.get("difficulty", 1),
                    "monsters": map_data.get("monsters", []),
                    "is_dungeon": map_data.get("is_dungeon", False),
                    "is_world_boss": map_data.get("is_world_boss", False),
                    "dungeon_type": map_data.get("dungeon_type", ""),
                    "time_limit": map_data.get("time_limit", 0),
                    "unlocked": map_data.get("level_required", 1) <= 1,  # 如果等级需求为1，则默认解锁
                    "unlock_requirement": {
                        "player_level": map_data.get("level_required", 1)
                    },
                    # 为UI面板添加额外信息
                    "monsters_info": f"怪:{len(map_data.get('monsters', []))}",
                    "boss_info": "Boss:1" if map_data.get("is_world_boss", False) else "Boss:0",
                    "challenge1_name": "鹿王",  # 默认挑战名称
                    "challenge1_stored": 0,    # 默认存储数量
                    "unlock_req_kill": map_data.get("unlock_req_kill", 100),  # 杀怪需求
                    "unlock_req_boss_kill": map_data.get("unlock_req_boss_kill", 5),  # 杀BOSS需求
                    "category": "玛法"  # 默认分类
                }
                
                # 根据地图类型设置分类
                if "副本" in map_name:
                    map_entry["category"] = "副本"
                elif map_data.get("is_world_boss", False):
                    map_entry["category"] = "世界Boss"
                elif map_data.get("is_dungeon", False) and "塔" in map_name:
                    map_entry["category"] = "幻境塔"
                
                self.available_maps.append(map_entry)
                map_id += 1
            
            # 按照难度排序
            self.available_maps.sort(key=lambda x: x["difficulty"])
            
            # 标记一些默认的世界BOSS地图（演示用）
            # 在实际项目中，这部分应该来自配置文件
            world_boss_names = ["虹魔教主", "赤月恶魔", "祖玛教主"]
            for map_data in self.available_maps:
                for boss_name in world_boss_names:
                    if boss_name in map_data["name"]:
                        map_data["is_world_boss"] = True
                        map_data["category"] = "世界Boss"
                        map_data["boss_info"] = "Boss:1"
                        # 如果是世界BOSS，则不是普通副本
                        map_data["is_dungeon"] = False
            
            print(f"成功加载了 {len(self.available_maps)} 个地图")
        except Exception as e:
            print(f"加载地图配置失败: {e}")
            # 使用默认地图数据作为备用
            self.available_maps = [
                {
                    "id": "map_001", 
                    "name": "新手村", 
                    "unlocked": True, 
                    "difficulty": 1,
                    "category": "玛法",
                    "monsters_info": "怪:5",
                    "boss_info": "Boss:0",
                    "challenge1_name": "鹿王",
                    "challenge1_stored": 0,
                    "unlock_req_kill": 50,
                    "unlock_req_boss_kill": 2
                },
                {
                    "id": "map_002", 
                    "name": "银杏村", 
                    "unlocked": True, 
                    "difficulty": 2,
                    "category": "玛法",
                    "monsters_info": "怪:8",
                    "boss_info": "Boss:0",
                    "challenge1_name": "鹿王",
                    "challenge1_stored": 0,
                    "unlock_req_kill": 100,
                    "unlock_req_boss_kill": 3
                },
                {
                    "id": "map_003", 
                    "name": "比奇大陆", 
                    "unlocked": True, 
                    "difficulty": 3,
                    "category": "玛法",
                    "monsters_info": "怪:12",
                    "boss_info": "Boss:0",
                    "challenge1_name": "鹿王",
                    "challenge1_stored": 0,
                    "unlock_req_kill": 200,
                    "unlock_req_boss_kill": 5
                },
                {
                    "id": "map_004", 
                    "name": "盟重省", 
                    "unlocked": False, 
                    "difficulty": 4, 
                    "unlock_requirement": {"player_level": 40},
                    "category": "玛法",
                    "monsters_info": "怪:15",
                    "boss_info": "Boss:0",
                    "challenge1_name": "鹿王",
                    "challenge1_stored": 0,
                    "unlock_req_kill": 300,
                    "unlock_req_boss_kill": 8
                },
                {
                    "id": "map_005", 
                    "name": "虹魔教主", 
                    "unlocked": False, 
                    "difficulty": 5, 
                    "unlock_requirement": {"player_level": 50},
                    "category": "世界Boss",
                    "is_world_boss": True,
                    "monsters_info": "怪:0",
                    "boss_info": "Boss:1",
                    "challenge1_name": "虹魔教主",
                    "challenge1_stored": 0,
                    "unlock_req_kill": 500,
                    "unlock_req_boss_kill": 10
                },
                {
                    "id": "map_006", 
                    "name": "赤月恶魔", 
                    "unlocked": False, 
                    "difficulty": 6, 
                    "unlock_requirement": {"player_level": 55},
                    "category": "世界Boss",
                    "is_world_boss": True,
                    "monsters_info": "怪:0",
                    "boss_info": "Boss:1",
                    "challenge1_name": "赤月恶魔",
                    "challenge1_stored": 0,
                    "unlock_req_kill": 800,
                    "unlock_req_boss_kill": 15
                },
                {
                    "id": "map_007", 
                    "name": "幻境九层塔", 
                    "unlocked": False, 
                    "difficulty": 7, 
                    "unlock_requirement": {"player_level": 60},
                    "category": "幻境塔",
                    "is_dungeon": True,
                    "monsters_info": "怪:30",
                    "boss_info": "Boss:1",
                    "challenge1_name": "塔主",
                    "challenge1_stored": 0,
                    "unlock_req_kill": 1000,
                    "unlock_req_boss_kill": 20
                }
            ]
    
    def get_maps_by_category(self, category):
        """
        根据分类获取地图列表
        
        Args:
            category: 地图分类（"玛法"、"世界Boss"、"幻境塔"、"副本"）
        
        Returns:
            该分类下的地图列表
        """
        if category == "副本":
            # 筛选名称中包含"副本"的地图
            return [map_data for map_data in self.available_maps if "副本" in map_data.get("name", "")]
        else:
            return [map_data for map_data in self.available_maps if map_data.get("category", "玛法") == category]
    
    def get_boss_attempts(self):
        """
        获取当前BOSS挑战次数信息
        
        Returns:
            格式化的BOSS次数字符串，如 "15/15"
        """
        current = self.boss_challenge.get("current", 0)
        max_daily = self.boss_challenge.get("max_daily", 15)
        return f"{current}/{max_daily}"
    
    def get_current_map_boss_info(self, map_data):
        """
        获取当前地图的Boss信息
        
        Args:
            map_data: 地图数据
            
        Returns:
            Boss信息字典，包含名称、刷新状态等
        """
        if not map_data:
            return None
            
        # 检查是否有Boss
        boss_info = map_data.get("boss_info", "Boss:0")
        if boss_info == "Boss:0":
            return None
            
        map_name = map_data.get("name", "")
        
        # 获取Boss刷新时间配置
        respawn_time = 300  # 默认5分钟刷新时间
        if hasattr(self, 'maps_config') and map_name in self.maps_config:
            respawn_time = self.maps_config[map_name].get("respawn_time", 300)
            
        # 检查Boss是否已刷新
        current_time = time.time()
        last_kill_time = self.boss_respawn_times.get(map_name, 0)
        is_available = (current_time - last_kill_time) >= respawn_time
        
        return {
            "name": map_name,
            "has_boss": True,
            "is_available": is_available,
            "respawn_time": respawn_time,
            "time_until_respawn": max(0, respawn_time - (current_time - last_kill_time))
        }
    
    def toggle_auto_challenge(self):
        """
        切换自动挑战Boss状态
        
        Returns:
            当前自动挑战状态
        """
        self.auto_challenge_enabled = not self.auto_challenge_enabled
        return self.auto_challenge_enabled
    
    def can_challenge_boss(self, map_data):
        """
        检查是否可以挑战Boss
        
        Args:
            map_data: 地图数据
            
        Returns:
            是否可以挑战Boss
        """
        # 检查是否有挑战次数
        if self.boss_challenge_storage <= 0:
            return False
            
        # 检查地图是否有Boss
        boss_info = self.get_current_map_boss_info(map_data)
        if not boss_info or not boss_info["has_boss"]:
            return False
            
        # 检查Boss是否已刷新
        return boss_info["is_available"]
    
    def start_boss_challenge(self, map_data, is_manual=False):
        """
        开始Boss挑战
        
        Args:
            map_data: 地图数据
            is_manual: 是否为手动挑战
            
        Returns:
            是否成功开始挑战
        """
        if not self.can_challenge_boss(map_data):
            return False
            
        # 消耗挑战次数
        self.boss_challenge_storage -= 1
        
        # 记录Boss击杀时间（模拟）
        map_name = map_data.get("name", "")
        self.boss_respawn_times[map_name] = time.time()
        
        # 这里可以添加实际的Boss战斗逻辑
        print(f"开始挑战Boss: {map_name} ({'手动' if is_manual else '自动'})")
        
        return True
    
    def get_next_unlockable_map_reqs(self, player):
        """
        获取下一个可解锁地图的需求信息
        
        Args:
            player: 玩家对象
        
        Returns:
            包含解锁需求的字典，如果没有可解锁地图则返回None
        """
        # 找到第一个未解锁的地图
        for map_data in self.available_maps:
            if not map_data.get("unlocked", False):
                # 检查玩家是否满足等级要求
                unlock_req = map_data.get("unlock_requirement", {})
                required_level = unlock_req.get("player_level", 1)
                
                if player.level >= required_level:
                    # 玩家等级满足，返回其他解锁需求
                    return {
                        "map_name": map_data.get("name", "未知地图"),
                        "kill": map_data.get("unlock_req_kill", 100),
                        "boss": map_data.get("unlock_req_boss_kill", 5)
                    }
        
        # 没有找到可解锁的地图
        return None
    
    def get_map_by_id(self, map_id):
        for map_data in self.available_maps:
            if map_data["id"] == map_id:
                return map_data
        return None
    
    def get_random_enemy(self, map_id):
        """
        根据地图ID获取随机怪物
        """
        map_data = self.get_map_by_id(map_id)
        if not map_data or "monsters" not in map_data or not map_data["monsters"]:
            # 返回默认怪物数据
            return {"name": "小怪", "hp": 100, "max_hp": 100, "level": 30}
    
        # 根据权重随机选择怪物
        monsters = map_data["monsters"]
        total_weight = sum(monster.get("weight", 1) for monster in monsters)
        
        if total_weight <= 0:
            return {"name": "小怪", "hp": 100, "max_hp": 100, "level": 30}
        
        random_value = random.randint(1, total_weight)
        current_weight = 0
        
        for monster in monsters:
            current_weight += monster.get("weight", 1)
            if random_value <= current_weight:
                # 创建怪物实例
                monster_level = max(1, map_data.get("difficulty", 1) * 5)  # 根据地图难度设置怪物等级
                monster_hp = monster_level * 20  # 简单的HP计算公式
                
                return {
                    "name": monster["name"],
                    "hp": monster_hp,
                    "max_hp": monster_hp,
                    "level": monster_level
                }
        
        # 默认情况下返回第一个怪物
        return {"name": monsters[0]["name"], "hp": 100, "max_hp": 100, "level": 30}
    
    def check_map_unlock_requirement(self, map_id, player):
        map_data = self.get_map_by_id(map_id)
        if not map_data:
            return False
        
        if "unlock_requirement" not in map_data:
            return True
        
        req = map_data["unlock_requirement"]
        if "player_level" in req and player.level < req["player_level"]:
            return False
        
        return True
    
    def unlock_map(self, map_id):
        for map_data in self.available_maps:
            if map_data["id"] == map_id:
                map_data["unlocked"] = True
                return True
        return False

# 创建简单的BattleManager类
class BattleManager:
    def __init__(self):
        self.enemies = []
        self.battle_logs = []
        self.battle_state = "idle"  # idle, fighting, victory, defeat
    
    def get_enemies(self):
        return self.enemies
    
    def get_battle_log(self):
        return self.battle_logs
    
    def get_battle_state(self):
        return self.battle_state
    
    # 已移除重复的 start_battle 方法
    # 现在统一使用 BattleManager 中的 start_battle 方法

# 创建简单的RankManager类
class RankManager:
    def __init__(self):
        self.rank_types = ["battle_power", "level"]
        self.current_rank_type = "battle_power"
        self.current_page = 0
        self.ranks_per_page = 10
        
        # 模拟排行榜数据
        self.battle_power_ranks = [
            {"rank": 1, "name": "玩家1", "battle_power": 15341},
            {"rank": 2, "name": "玩家2", "battle_power": 14567},
            {"rank": 3, "name": "玩家3", "battle_power": 13982},
            {"rank": 4, "name": "玩家4", "battle_power": 12678},
            {"rank": 5, "name": "玩家5", "battle_power": 11234},
            {"rank": 6, "name": "玩家6", "battle_power": 10987},
            {"rank": 7, "name": "玩家7", "battle_power": 9876},
            {"rank": 8, "name": "玩家8", "battle_power": 8765},
            {"rank": 9, "name": "玩家9", "battle_power": 7654},
            {"rank": 10, "name": "玩家10", "battle_power": 6543}
        ]
        
        self.level_ranks = [
            {"rank": 1, "name": "玩家A", "level": 85},
            {"rank": 2, "name": "玩家B", "level": 83},
            {"rank": 3, "name": "玩家C", "level": 80},
            {"rank": 4, "name": "玩家D", "level": 78},
            {"rank": 5, "name": "玩家E", "level": 75},
            {"rank": 6, "name": "玩家F", "level": 73},
            {"rank": 7, "name": "玩家G", "level": 70},
            {"rank": 8, "name": "玩家H", "level": 68},
            {"rank": 9, "name": "玩家I", "level": 65},
            {"rank": 10, "name": "玩家J", "level": 63}
        ]
    
    def get_current_ranks(self):
        if self.current_rank_type == "battle_power":
            return self.battle_power_ranks
        else:
            return self.level_ranks
    
    def set_current_rank_type(self, rank_type):
        if rank_type in self.rank_types:
            self.current_rank_type = rank_type
            self.current_page = 0
            return True
        return False
    
    def prev_page(self):
        if self.current_page > 0:
            self.current_page -= 1
            return True
        return False
    
    def next_page(self):
        total_ranks = len(self.get_current_ranks())
        max_page = (total_ranks - 1) // self.ranks_per_page
        
        if self.current_page < max_page:
            self.current_page += 1
            return True
        return False

def open_skill_panel(player):
    """
    打开技能面板
    
    Args:
        player: 玩家对象
    """
    print("正在打开技能面板...")
    
    # 由于技能面板是基于tkinter的，而主界面是基于pygame的，
    # 我们需要在单独的线程中运行tkinter的主循环
    def run_skill_panel():
        # 将玩家对象引用到内部
        current_player = player
        
        try:
            # 创建一个新的tkinter根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            print("✅ Tkinter根窗口创建成功")
            
            # 创建技能面板
            print("🔧 开始创建技能面板...")
            
            # 先检查技能面板类是否正确导入
            from game.ui.skill_panel import SkillPanel
            print("✅ SkillPanel类导入成功")
            
            skill_panel = SkillPanel(root, current_player)
            print("✅ 技能面板创建成功")
            
            # 运行tkinter主循环
            print("🔧 启动Tkinter主循环...")
            root.mainloop()
            print("🔚 Tkinter主循环结束")
            
        except Exception as e:
            print(f"❌ 打开技能面板失败：{e}")
            import traceback
            traceback.print_exc()
            
            # 如果技能面板失败，尝试打开简化版本
            print("🔧 尝试打开简化技能面板...")
            try:
                open_simple_skill_panel(current_player)
            except Exception as simple_e:
                print(f"❌ 简化技能面板也失败：{simple_e}")
                # 最后的提示
                print("💡 提示：")
                print("   1. 如果遇到PIL错误，请安装Pillow: pip install Pillow")
                print("   2. 如果图片加载错误，请在技能面板中切换到安全模式")
                print("   3. 技能面板会根据环境自动选择最合适的模式")
    
    # 创建并启动线程
    skill_thread = Thread(target=run_skill_panel)
    skill_thread.daemon = True  # 设置为守护线程，这样当主线程退出时，这个线程也会退出
    skill_thread.start()

def open_simple_skill_panel(player):
    """
    简化版技能面板，不加载图片，只显示技能信息
    """
    print("🔧 创建简化技能面板（无图片）...")
    
    root = tk.Tk()
    root.title("技能面板（简化版）")
    root.geometry("600x400")
    root.configure(bg="#2D2D2D")
    
    # 标题
    title_label = tk.Label(
        root,
        text="技能面板（简化版）",
        bg="#2D2D2D",
        fg="#FFFFFF",
        font=("微软雅黑", 14, "bold")
    )
    title_label.pack(pady=10)
    
    # 提示信息
    info_label = tk.Label(
        root,
        text="注意：此为简化版本，不加载技能图标\n如需完整功能，请检查图片资源",
        bg="#2D2D2D",
        fg="#FFFF00",
        font=("微软雅黑", 10)
    )
    info_label.pack(pady=5)
    
    # 技能列表框架
    skills_frame = tk.Frame(root, bg="#2D2D2D")
    skills_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
    
    # 添加一些示例技能
    skills_info = [
        "基本剑法 - 被动技能，提高准确性",
        "攻杀剑术 - 被动技能，增加伤害",
        "火球术 - 主动技能，远程攻击",
        "治愈术 - 主动技能，恢复生命",
        "雷电术 - 主动技能，电系攻击"
    ]
    
    for i, skill_info in enumerate(skills_info):
        skill_label = tk.Label(
            skills_frame,
            text=f"{i+1}. {skill_info}",
            bg="#3D3D3D",
            fg="#FFFFFF",
            font=("微软雅黑", 10),
            anchor="w",
            pady=5,
            padx=10
        )
        skill_label.pack(fill=tk.X, pady=2)
    
    # 关闭按钮
    close_btn = tk.Button(
        root,
        text="关闭",
        bg="#FF6666",
        fg="#FFFFFF",
        font=("微软雅黑", 10),
        command=root.destroy
    )
    close_btn.pack(pady=10)
    
    print("✅ 简化技能面板创建成功")
    root.mainloop()

def open_skill_hotbar(player):
    """
    打开自动技能栏
    
    Args:
        player: 玩家对象
    """
    print("正在打开自动技能栏...")
    
    def run_skill_hotbar():
        current_player = player
        
        try:
            # 创建一个新的tkinter根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            
            # 创建自动技能栏
            skill_hotbar = AutoSkillHotbar(root, current_player)
            
            # 运行tkinter主循环
            root.mainloop()
        except Exception as e:
            print(f"打开自动技能栏失败：{e}")
    
    # 创建并启动线程
    hotbar_thread = Thread(target=run_skill_hotbar)
    hotbar_thread.daemon = True
    hotbar_thread.start()

def main():
    """主函数"""
    try:
        print("正在初始化游戏...")
        
        # 初始化资源管理器
        from game.core.resource_manager import init_resource_manager
        init_resource_manager()
        
        # 初始化pygame
        pygame.init()
        print("Pygame初始化完成")
        
        # 创建游戏窗口
        screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("萝卜传奇")
        print("游戏窗口创建完成")
        
        # 初始化背景图片加载
        from game.core.resource_manager import get_game_asset_path
        background_path = get_game_asset_path("images/background.png")
        if os.path.exists(background_path):
            print(f"✅ 找到背景图片: {background_path}")
            try:
                background = pygame.image.load(background_path).convert()
                print(f"✅ 背景图片加载成功，尺寸: {background.get_size()}")
            except Exception as e:
                print(f"❌ 背景图片加载失败: {e}")
                background = None
        else:
            print(f"❌ 背景图片不存在: {background_path}")
            background = None
        
        # pygame.display初始化完成后，初始化需要display的管理器
        from game.managers.enemy_image_manager import enemy_image_manager
        enemy_image_manager.initialize_after_display()
        
        # 创建时钟对象，用于控制游戏帧率
        clock = pygame.time.Clock()
        
        # 游戏状态控制
        game_state = "start_menu"  # start_menu, character_creation, main_game, exit
        
        # 导入面板类
        from game.ui.start_menu_panel import StartMenuPanel
        from game.ui.character_creation_panel import CharacterCreationPanel
        from game.managers.save_load_manager import SaveLoadManager
        
        # 创建开始菜单面板
        start_menu = StartMenuPanel(screen)
        character_creation = None
        ui_manager = None
        player = None
        
        # 设置开始菜单回调函数
        def handle_start_local_game():
            """处理开始本地游戏"""
            nonlocal game_state, character_creation, ui_manager, player
            print("🎮 开始本地游戏...")
            
            # 检查是否有本地存档
            save_manager = SaveLoadManager()
            save_list = save_manager.get_save_list()
            
            # 查找自动存档
            autosave_found = False
            for save_info in save_list:
                if save_info.get('name') == 'autosave':
                    autosave_found = True
                    print(f"📁 找到存档: {save_info.get('player_name', '未知')} 等级{save_info.get('player_level', 1)}")
                    break
            
            if autosave_found:
                # 有存档，直接进入游戏
                print("✅ 发现本地存档，正在加载...")
                game_state = "main_game"
                player = load_existing_game()
            else:
                # 没有存档，进入角色创建界面
                print("📝 未发现本地存档，进入角色创建界面...")
                game_state = "character_creation"
                character_creation = CharacterCreationPanel(
                    screen, 
                    position=(0, 0), 
                    size=(WINDOW_WIDTH, WINDOW_HEIGHT)
                )
                character_creation.set_callbacks(
                    on_create_character=handle_create_character,
                    on_back_to_menu=handle_back_to_menu
                )
        
        def handle_start_online_game():
            """处理开始联网游戏"""
            print("🌐 联网游戏功能正在开发中...")
            # 这里可以添加联网游戏的逻辑
        
        def handle_exit_game():
            """处理退出游戏"""
            nonlocal game_state
            print("👋 退出游戏...")
            game_state = "exit"
        
        def handle_create_character(character_data):
            """处理创建角色"""
            nonlocal game_state, player
            print(f"🎯 创建角色: {character_data}")
            
            # 创建新玩家对象
            player = Player(
                name=character_data['name'],
                character_class=character_data['class'],
                gender=character_data['gender']
            )
            
            # 保存新玩家数据作为自动存档
            save_manager = SaveLoadManager()
            if save_manager.save_player_data(player, "autosave"):
                print("✅ 新角色创建并保存成功")
                game_state = "main_game"
            else:
                print("❌ 角色保存失败")
        
        def handle_back_to_menu():
            """处理返回主菜单"""
            nonlocal game_state
            print("🔙 返回主菜单...")
            game_state = "start_menu"
        
        def load_existing_game():
            """加载现有游戏"""
            save_manager = SaveLoadManager()
            player_data = save_manager.load_player_data("autosave")
            
            if player_data:
                # 从存档数据创建玩家对象
                player = Player(data=player_data)
                print(f"✅ 成功加载玩家: {player.name} (等级{player.level})")
                return player
            else:
                print("❌ 存档数据加载失败")
                return None
        
        def initialize_main_game():
            """初始化主游戏界面"""
            nonlocal ui_manager
            
            print("正在初始化主游戏界面...")
            
            # 创建数据管理器
            from game.managers.data_manager import DataManager
            data_manager = DataManager()
            print("数据管理器创建完成")
            
            # 导入并使用正确的MapManager
            from game.core.map_manager import MapManager as CoreMapManager
            map_manager = CoreMapManager(data_manager)
            print("地图管理器创建完成")
            print(f"当前地图: {map_manager.current_map.name if map_manager.current_map else 'None'}")
            
            from game.core.battle_manager import BattleManager as CoreBattleManager
            battle_manager = CoreBattleManager()
            # 设置数据管理器引用，用于获取真实怪物数据
            battle_manager.data_manager = data_manager
            print("战斗管理器创建完成")
            
            # 设置地图管理器和战斗管理器的相互引用
            map_manager.battle_manager = battle_manager
            map_manager.player_instance = player
            battle_manager.map_manager = map_manager
            print("设置管理器相互引用完成")
            
            rank_manager = RankManager()
            print("排行榜管理器创建完成")
            
            # 创建UI管理器
            print("正在创建UI管理器...")
            ui_manager = UIManager(screen, player, map_manager, battle_manager, rank_manager)
            print("UI管理器创建完成")
            
            # 🔧 新增：为UI管理器添加保存管理器引用
            save_manager = SaveLoadManager()
            ui_manager.save_manager = save_manager
            
            # 设置技能面板回调函数
            ui_manager.set_skill_panel_callback(lambda: open_skill_panel(player))
            
            # 设置装备面板回调函数
            ui_manager.set_equipment_panel_callback()
            
            # 设置背包面板回调函数
            ui_manager.set_inventory_panel_callback()
            
            # 设置仓库面板回调函数
            ui_manager.set_warehouse_panel_callback()
            
            # 设置药水面板回调函数
            ui_manager.set_medicine_panel_callback()
            
            # 设置签到面板回调函数
            ui_manager.set_checkin_panel_callback()
            
            # 设置积分商城面板回调函数
            ui_manager.set_shop_panel_callback()
            
            # 设置金币商城面板回调函数
            ui_manager.set_gold_shop_panel_callback()
            
            return ui_manager
        
        # 设置开始菜单回调函数
        start_menu.set_callbacks(
            on_start_local=handle_start_local_game,
            on_start_online=handle_start_online_game,
            on_settings=None,  # 设置功能待实现
            on_exit=handle_exit_game
        )
        
        # 游戏主循环
        print("开始游戏主循环...")
        running = True
        while running and game_state != "exit":
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    if ui_manager and ui_manager.battle_panel and hasattr(ui_manager.battle_panel, 'battle_active') and ui_manager.battle_panel.battle_active:
                        if hasattr(ui_manager.battle_panel, 'end_battle'):
                            ui_manager.battle_panel.end_battle()
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        if ui_manager and ui_manager.battle_panel and hasattr(ui_manager.battle_panel, 'battle_active') and ui_manager.battle_panel.battle_active:
                            if hasattr(ui_manager.battle_panel, 'end_battle'):
                                ui_manager.battle_panel.end_battle()
                        else:
                            running = False
                    # 禁用技能快捷键，改为使用战斗区域的"自动"按钮
                    # elif event.key == pygame.K_h:  # H键打开技能栏
                    #     open_skill_hotbar(player)
                    # elif event.key == pygame.K_k:  # K键打开技能面板（学习技能）
                    #     open_skill_panel(player)
                # 根据游戏状态处理事件
                if game_state == "start_menu":
                    start_menu.handle_event(event)
                elif game_state == "character_creation" and character_creation:
                    character_creation.handle_event(event)
                elif game_state == "main_game" and ui_manager:
                    ui_manager.handle_event(event)
            
            # 获取当前时间
            current_time = pygame.time.get_ticks()
            
            # 清空屏幕
            screen.fill(BLACK)
            
            # 根据游戏状态更新和渲染
            if game_state == "start_menu":
                start_menu.update()
                start_menu.render()
            elif game_state == "character_creation" and character_creation:
                character_creation.update()
                character_creation.render()
            elif game_state == "main_game":
                if ui_manager is None:
                    # 首次进入主游戏，初始化UI管理器
                    ui_manager = initialize_main_game()
                
                if ui_manager:
                    # 更新和渲染主游戏UI
                    ui_manager.update(current_time)
                    ui_manager.render()
                    
                    # 🔧 新增：定期自动保存
                    if hasattr(ui_manager, 'save_manager') and ui_manager.save_manager:
                        ui_manager.save_manager.check_auto_save(player)
            
            # 更新屏幕
            pygame.display.flip()
            
            # 控制帧率
            clock.tick(60)
        
        print("游戏主循环结束")
        
    except Exception as e:
        print(f"游戏运行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 退出pygame
        pygame.quit()
        print("游戏已退出")

if __name__ == "__main__":
    main()