import pygame
from game.ui.ui_panel import UIPanel

# 导入新的日志系统
from game.core.log_manager import get_log_manager
from game.managers.user_message_manager import UserMessageType, UserMessagePriority

class AnnouncementPanel(UIPanel):
    """
    公告面板 - 聊天框形式的公告区域
    支持置顶公告和玩家输入消息
    支持中文输入法（IME）
    """
    def __init__(self, screen, battle_manager, position, size, player=None, user_message_manager=None):
        """
        初始化公告面板
        
        Args:
            screen: 屏幕对象
            battle_manager: 战斗管理器
            position: 面板位置 (x, y)
            size: 面板大小 (width, height)
            player: 玩家对象（可选）
            user_message_manager: 用户消息管理器（可选）
        """
        super().__init__(screen, position, size)
        
        # 管理器和玩家引用
        self.battle_manager = battle_manager
        self.player = player
        self.user_message_manager = user_message_manager
        self.log_manager = get_log_manager()

        # 面板基本属性
        self.title = "系统公告"
        self.background_color = (25, 25, 25)
        self.title_font = pygame.font.SysFont("SimHei", 16, bold=True)
        self.normal_font = pygame.font.SysFont("SimHei", 14)

        # 消息列表
        self.pinned_announcements = [
            {"message": "【系统公告】欢迎回家", "color": (255, 215, 0), "pinned": True, "time": pygame.time.get_ticks()},
            {"message": "【活动公告】", "color": (255, 165, 0), "pinned": True, "time": pygame.time.get_ticks()}
        ]
        self.chat_messages = []
        self.all_messages = []

        # UI 布局
        self.message_area_height = self.size[1] - 80
        self.message_rect = pygame.Rect(self.rect.left + 10, self.rect.top + 30, self.size[0] - 20, self.message_area_height)
        
        self.input_height = 30
        self.input_rect = pygame.Rect(self.rect.left + 10, self.rect.bottom - self.input_height - 10, self.size[0] - 80, self.input_height)
        self.send_button_rect = pygame.Rect(self.input_rect.right + 5, self.input_rect.top, 65, self.input_height)

        # 输入相关属性
        self.input_text = ""
        self._input_active = False
        self.cursor_pos = 0
        self.cursor_visible = True
        self.cursor_timer = 0
        self.max_input_length = 100
        
        # IME 输入法属性
        self.ime_editing_text = ""
        self.ime_editing_start = 0
        self.ime_editing_length = 0

        # 消息显示属性
        self.message_line_height = 18
        self.visible_messages = self.message_rect.height // self.message_line_height
        self.message_scroll_position = 0

        # 滚动属性
        self.scroll_speed = 3
        self.smooth_scroll = True
        self.auto_scroll_to_bottom = True

        # 文本选择属性
        self.text_selection = {'active': False, 'start_pos': None, 'end_pos': None, 'dragging': False}

        # 玩家名称
        self.player_name = getattr(self.player, 'name', '玩家') if self.player else '玩家'

        # 初始化消息
        self.update_all_messages()

    def update_all_messages(self):
        """
        更新所有消息列表（置顶公告 + 聊天消息 + 系统消息）
        """
        self.all_messages = self.pinned_announcements.copy() + self.chat_messages.copy()
        
        # 从UserMessageManager获取消息并合并
        if self.user_message_manager:
            system_messages = self.user_message_manager.get_all_messages()
            for msg in system_messages:
                # 将UserMessage对象转换为面板可以渲染的字典格式
                self.all_messages.append({
                    "message": f"【系统】{msg.message}",
                    "color": msg.color,
                    "pinned": msg.is_pinned,
                    "time": msg.timestamp * 1000  # 转换为毫秒
                })
        
        # 按时间排序
        self.all_messages.sort(key=lambda x: x.get('time', 0))

        # 自动滚动到最新消息
        if self.auto_scroll_to_bottom:
            self.message_scroll_position = max(0, len(self.all_messages) - self.visible_messages)

    def add_chat_message(self, player_name, message, color=(200, 200, 200)):
        """
        添加聊天消息
        """
        chat_message = {
            "message": f"[{player_name}]: {message}",
            "color": color,
            "pinned": False,
            "time": pygame.time.get_ticks(),
            "player": player_name
        }
        self.chat_messages.append(chat_message)
        if len(self.chat_messages) > 50:
            self.chat_messages.pop(0)
        self.update_all_messages()

    def update(self):
        """
        更新面板状态
        """
        # 更新光标闪烁
        self.cursor_timer += 1
        if self.cursor_timer >= 30:
            self.cursor_visible = not self.cursor_visible
            self.cursor_timer = 0
        
        # 每次更新时都重新整合消息
        self.update_all_messages()
    
    def render(self):
        """
        渲染公告面板
        """
        if not self.visible:
            return
        
        super().render() # 使用UIPanel的render方法渲染背景和标题
        
        self.render_messages()
        self.render_input_box()
        self.render_send_button()

    def render_messages(self):
        """
        渲染消息区域
        """
        pygame.draw.rect(self.screen, (15, 15, 15), self.message_rect)
        pygame.draw.rect(self.screen, (60, 60, 60), self.message_rect, 1)
        
        start_index = self.message_scroll_position
        end_index = min(start_index + self.visible_messages, len(self.all_messages))
        
        for i in range(start_index, end_index):
            message_data = self.all_messages[i]
            message_y = self.message_rect.top + (i - start_index) * self.message_line_height
            
            message_text = str(message_data["message"])
            message_color = message_data["color"]
            
            message_surface = self.normal_font.render(message_text, True, message_color)
            self.screen.blit(message_surface, (self.message_rect.left + 5, message_y))
    
    def set_input_active(self, active_status: bool):
        if self._input_active == active_status:
            return
        self._input_active = active_status
        if self._input_active:
            pygame.key.start_text_input()
            pygame.key.set_text_input_rect(self.input_rect)
        else:
            pygame.key.stop_text_input()

    def send_message(self):
        if self.input_text.strip():
            self.add_chat_message(self.player_name, self.input_text.strip(), (150, 255, 150))
            self.input_text = ""
            self.cursor_pos = 0

    def handle_event(self, event):
        if not self.visible:
            return False
        
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            if self.send_button_rect.collidepoint(event.pos):
                self.send_message()
                return True
            elif self.input_rect.collidepoint(event.pos):
                self.set_input_active(True)
                return True
            else:
                self.set_input_active(False)

        if self._input_active and event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
                self.send_message()
            elif event.key == pygame.K_BACKSPACE:
                self.input_text = self.input_text[:-1]
            else:
                self.input_text += event.unicode
            return True
        return False

    def render_input_box(self):
        pygame.draw.rect(self.screen, (40, 40, 40), self.input_rect)
        pygame.draw.rect(self.screen, (80, 80, 80), self.input_rect, 1)
        text_surface = self.normal_font.render(self.input_text, True, (255, 255, 255))
        self.screen.blit(text_surface, (self.input_rect.x + 5, self.input_rect.y + 5))
        if self._input_active and self.cursor_visible:
            cursor_x = self.input_rect.x + 5 + text_surface.get_width()
            pygame.draw.line(self.screen, (255, 255, 255), (cursor_x, self.input_rect.y + 5), (cursor_x, self.input_rect.y + self.input_height - 5))

    def render_send_button(self):
        pygame.draw.rect(self.screen, (60, 120, 170), self.send_button_rect)
        text_surface = self.normal_font.render("发送", True, (255, 255, 255))
        text_rect = text_surface.get_rect(center=self.send_button_rect.center)
        self.screen.blit(text_surface, text_rect)

    # 这些方法需要被完整地恢复，以确保聊天框功能正常
    # ...